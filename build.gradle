import org.sonarqube.gradle.SonarQubePlugin

buildscript {
    ext {
        springBootVersion = '3.5.3'
    }
    repositories {
        maven {
            url 'https://nexus.tmbbank.local:8081/repository/oneapp'
            credentials {
                username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
                password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
            }
        }
        maven {
            url "https://nexus.tmbbank.local:8081/repository/plugins.gradle/"
            credentials {
                username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
                password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
            }
        }
    }

    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath "com.palantir.gradle.docker:gradle-docker:0.35.0"
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:4.4.1.3373"
    }
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: "com.palantir.docker"
apply plugin: 'jacoco'
apply plugin: SonarQubePlugin
group = 'com.tmb.oneapp'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

if (project.hasProperty('projVersion')) {
    project.version = project.projVersion
} else {
    project.version = '15.0.0'
}

test {
    useJUnitPlatform()
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    maven {
        url 'https://nexus.tmbbank.local:8081/repository/oneapp'
        credentials {
            username = project.hasProperty('repoUsername') ? project.getProperty('repoUsername') : 'oneapp_lib'
            password = project.hasProperty('repoPassword') ? project.getProperty('repoPassword') : 'ad123Tmb*'
        }
    }

}

springBoot {
    buildInfo()
}

ext {
    set('springCloudVersion', "2025.0.0")
    set('log4j2.version',"2.17.1")
}

dependencies {
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'org.springframework.retry:spring-retry'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'

    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.0.2'

    implementation 'commons-io:commons-io:2.15.1'

    implementation 'org.apache.commons:commons-pool2:2.11.1'
    implementation 'org.apache.commons:commons-lang3:3.13.0'
    implementation 'com.google.guava:guava:32.1.3-jre'
    implementation 'com.googlecode.json-simple:json-simple:1.1.1'
    implementation 'com.github.ulisesbocchio:jasypt-spring-boot-starter:2.1.0'
    implementation 'org.json:json:20231013'
    implementation 'org.mapstruct:mapstruct:1.5.3.Final'
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'commons-io:commons-io:2.15.1'

    implementation 'com.tmb.common:tmb_common_utility:3.2.1-rc.2'
    implementation 'com.tmb.common:oneapp-redis-client-lib:3.2.0-rc.2'
    implementation 'com.tmb.common:one-kafka-lib:1.1.0-rc.2'

    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'org.junit.platform:junit-platform-commons'

    compileOnly 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.projectlombok:lombok:1.18.30'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.3.Final'
}

bootRun {
    systemProperties System.properties
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

jar {
    enabled = false
    archiveClassifier = ''
}

docker {
    name "com.tmb.oneapp/${project.name}-jvm-17:${project.version}"
    dockerfile file('Dockerfile')
    files jar.archiveFile
    buildArgs(['JAR_FILE': "${jar.archiveFileName}"])
}

tasks.getByPath('dockerPrepare').dependsOn('bootJar')
tasks.getByPath('dockerPrepare').dependsOn('jar')
tasks.getByPath('docker').dependsOn('build')

jacoco {
    toolVersion = "0.8.11"
}


jacocoTestReport {
    reports {
        html.required = true
        xml.required = true
        csv.required = true
    }
}

sonarqube {
    if (System.getProperty("sonar.host.url").equals(null)) {
        properties {
            System.setProperty('sonar.host.url', 'http://localhost:9000')
        }
    }
    properties {
        property 'sonar.coverage.exclusions', '**/config/*, **/model/** ,**/constant/*, **/MbTransferServiceApplication.java, **/util/* , **/localcache/*'
    }
    properties {
        property 'sonar.exclusions', '**/config/*,**/AESSecurityUtil.java'
    }
}
test.finalizedBy jacocoTestReport
