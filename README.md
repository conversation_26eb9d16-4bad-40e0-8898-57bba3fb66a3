# Backend template


### Set spring profile activated via the environment variable
```
export spring_profiles_active=local
```

### Run application
```
./gradlew bootRun
```

### Build application
```
./gradlew build
```

### Code coverage report
```
/mb-transfer-service/build/reports/jacoco
```

### Swagger
```
http://localhost:8080/apis/backendtemplate/swagger-ui.html
when change package name shold be modify class SwaggerConfig.java 
```

### Unit Test Naming Conventions : Should_ExpectedBehavior_When_StateUnderTest
```
This technique is also used by many as it makes it easy to read the tests. 
Following are some of the example:
- Should_ThrowException_When_AgeLessThan18
- Should_FailToWithdrawMoney_ForInvalidAccount
- Should_FailToAdmit_IfMandatoryFieldsAreMissing
- 
```

### Basic Configuration
```
There are predefined configure in application.properties
for Loging,Kafka,CacheConfig,Redis,Oracle,MongoDB,Loging
```
### MountBank run test
```
PATH_TO_MB\mb start --configfile templates/imposters.ejs --allowInjection --loglevel debug
test url : http://localhost:8080/apis/backendtemplate/helloworld/test 
```