spring.application.name=mb-transfer-service
spring.application.description=MB-Transfer-Service
logging.level.com.tmb.oneapp.customercareexp.client.*=DEBUG
server.port=8080

jasypt.encryptor.password=RTHQB4FG92

spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER

swagger.host= https://apis-portal.oneapp.tmbbank.local,http://localhost
springdoc.api-docs.path=/v1/mb-transfer-service/api-docs
springdoc.swagger-ui.use-root-path=false
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true

log.feign.enable.all.api=true

#Loging
logging.config=classpath:logback.xml
logging.level.com.tmb.commonservice.feign.*=DEBUG

#Redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.pool.min-idle=0
spring.redis.pool.max-idle=8
spring.redis.pool.max-total=8
spring.redis.read-from=replicaPreferred
spring.redis.adaptive-refresh-trigger-timeout=15
spring.redis.periodic-refresh=15
spring.redis.mode=standalone

#interceptor
feign.client.config.default.requestInterceptors=com.tmb.common.interceptor.FeignRequestInterceptor

spring.kafka.producer.bootstrap-servers=***********:9092

utility.common.service.endpoint=common-service-https-internal-dev1-oneapp.apps.ddid1.tmbcps.com

management.endpoint.prometheus.enabled=true
management.endpoints.web.exposure.include=health,info,prometheus

feign.httpclient.enabled=true
