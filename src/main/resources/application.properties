spring.application.name=mb-transfer-service
spring.application.description=Mb-transfer-Service
logging.level.com.tmb.oneapp.mbtransferservice.client.*=DEBUG
server.port=8080

jasypt.encryptor.password=RTHQB4FG92
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
swagger.host=localhost:${server.port}
springdoc.api-docs.path=/v1/mb-transfer-service/api-docs
springdoc.swagger-ui.use-root-path=false
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true
log.feign.enable.all.api=true
#Loging
logging.config=classpath:logback.xml
logging.level.com.tmb.commonservice.feign.*=DEBUG
spring.jpa.show-sql=true
spring.jpa.database=oracle
spring.jpa.properties.hibernate.format_sql=true
app.api.logging.feign.enable=true
#Redis
spring.redis.host=***********
spring.redis.port=6379
spring.redis.pool.min-idle=0
spring.redis.pool.max-idle=8
spring.redis.pool.max-total=8
spring.redis.read-from=replicaPreferred
spring.redis.adaptive-refresh-trigger-timeout=15
spring.redis.periodic-refresh=15
spring.redis.mode=standalone
spring.main.allow-bean-definition-overriding=true

#Redis Library Feature Configuration
redis.cache.ttl.default=-1
redis.cache.enabled=true
redis.cache.custom.ttl=true
redis.template.enabled=true
redis.prefix.environment.name=${envconfig.oneapp.redis.prefix-default}
redis.prefix.cache-name.enabled=false

#interceptor
feign.client.config.default.requestInterceptors=com.tmb.common.interceptor.FeignRequestInterceptor
spring.kafka.producer.bootstrap-servers=***********:9092
utility.common.service.endpoint=common-service-https-internal-dev1-oneapp.apps.ddid1.tmbcps.com
management.endpoint.prometheus.enabled=true
management.endpoints.web.exposure.include=health,info,prometheus
feign.httpclient.enabled=true
feign.customers.service.name=customer-service
feign.customers.service.url=http://customers-service.dev1-oneapp.svc
feign.customers.exp.service.name=customer-exp-service
feign.customers.exp.service.url=http://customers-exp-service.dev1-oneapp.svc
feign.transfer.service.name=transfer-service
#feign.transfer.service.url=http://transfer-service-https-internal-dev1-oneapp.apps.ddid1.tmbcps.com
#feign.transfer.service.url=http://transfer-service-https-internal-dev1-oneapp.apps.ddid1.tmbcps.com
feign.transfer.service.url=http://transfer-service.dev1-oneapp.svc
feign.customers.account.biz.service.name=customer-account-biz-service
feign.customers.account.biz.service.url=http://customer-account.biz.dev4-oneapp.svc

feign.payment.exp.service.name=payment-exp-service
feign.payment.exp.service.url=http://payment-exp-service.dev4-oneapp.svc

fcd-td.allow-transfer.app-version=