package com.tmb.oneapp.mbtransferservice.model;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransferModuleModel {

    private String id;

    private CommonTime withdrawTdCutoffTime;

    private List<CommonFee> promptpayAccountFee;

    private String promptpayAccountTransLimit;

    private String promptpayProxyTransLimit;

    private List<CommonFee> promptpayProxyFee;

    private String promptPayProxyWaiveFee;

    private String exchangeTransLimit;

    private SchedulePromptPay schedulePromtpay;

    private BigDecimal frFinancialAccuAmount;

    private Boolean frScheduleFlag;

    private Boolean frTopupFlag;

    private Boolean frTransferFlag;

    private Boolean frTransferOttFlag;

    private BigDecimal frBillpayTransLimit;

    private BigDecimal frTransferTransLimit;

    private BigDecimal fcdTdMinimumAmount;
}
