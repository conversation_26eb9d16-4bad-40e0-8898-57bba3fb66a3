package com.tmb.oneapp.mbtransferservice.model.fx;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ExchangeRate {
    protected String imgUrl;
    protected String currencyDesc;
    protected String sbillRate;
    protected String ottRate;
    protected String rtCcy;
    protected String nostroSwiftCode;
    protected String seq;
}
