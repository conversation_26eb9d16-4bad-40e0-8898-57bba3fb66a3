package com.tmb.oneapp.mbtransferservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class V2DepositAccountTransfer {
    private List<DepositAccountTransfer> accountList;
    private List<DepositAccountTransfer> fcdAccountList;
}
