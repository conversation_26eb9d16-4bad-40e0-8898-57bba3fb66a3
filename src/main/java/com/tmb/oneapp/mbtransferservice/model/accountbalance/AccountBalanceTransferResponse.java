package com.tmb.oneapp.mbtransferservice.model.accountbalance;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@Accessors (chain = true)
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AccountBalanceTransferResponse {
    private String accountNumber;
    private String accountStatus;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal availableBalance;
    private String linkedAccount;
    private String accountName;

}
