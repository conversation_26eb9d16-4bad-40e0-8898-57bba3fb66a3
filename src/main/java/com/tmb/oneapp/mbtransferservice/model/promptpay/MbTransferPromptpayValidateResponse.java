package com.tmb.oneapp.mbtransferservice.model.promptpay;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.mbtransferservice.model.CommonAuthenticationInformation;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MbTransferPromptpayValidateResponse {
    private String transId;
    private String fee;
    private String amount;
    private Boolean isRequireConfirmPin;
    private String toAccountName;
    private Boolean isRequireFr;

    Boolean isRequireCommonAuthen;
    CommonAuthenticationInformation commonAuthenticationInformation;
}
