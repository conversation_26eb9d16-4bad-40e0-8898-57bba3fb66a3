package com.tmb.oneapp.mbtransferservice.model.onus;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.mbtransferservice.model.CommonAuthenticationInformation;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransferOnUsValidateResponse {
    private String transId;
    private String fee;
    private String amount;
    private Boolean isRequireConfirmPin;
    private String toAccountName;
    private String interest;
    private String principal;
    private String penalty;
    private String netAmount;
    private String tax;
    private Boolean isRequireFr;

    Boolean isRequireCommonAuthen;
    CommonAuthenticationInformation commonAuthenticationInformation;
}
