package com.tmb.oneapp.mbtransferservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class AccountDetailsData {
    private String accountNo;
    private String accountType;
    private String productCode;
    private String financialId;

    public AccountDetailsData(String accountNo, String accountType) {
        this.accountNo = accountNo;
        this.accountType = accountType;
    }

    public AccountDetailsData(String accountNo, String accountType, String financialId) {
        this.accountNo = accountNo;
        this.accountType = accountType;
        this.financialId = financialId;
    }
}