package com.tmb.oneapp.mbtransferservice.model.onus;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@NoArgsConstructor
@ToString
@Setter
@Getter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransferOnUsValidateRequest extends MbTransferOnUsValidateRequest {
    private DepositAccount depositAccount;
    private String fxTransId;
}
