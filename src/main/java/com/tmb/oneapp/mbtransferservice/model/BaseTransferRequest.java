package com.tmb.oneapp.mbtransferservice.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BaseTransferRequest {
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  @NotBlank(message = "from_account_no should not be blank")
  private String fromAccountNo;
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  @NotBlank(message = "to_account_no should not be blank")
  private String toAccountNo;
  private String toBankCode;
  private String toFavoriteName;
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  @NotBlank(message = "amount should not be blank")
  private String amount;
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private String categoryId;
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private String note;
  @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
  private String flow;
  private String qr;
  private String depositNo;
  private String fxTransId;
}
