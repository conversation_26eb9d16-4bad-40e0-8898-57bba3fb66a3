package com.tmb.oneapp.mbtransferservice.model;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface DepositAccountTransferMapper {
    DepositAccountTransferMapper INSTANCE = Mappers.getMapper(DepositAccountTransferMapper.class);

    DepositAccount toDepositAccount(DepositAccountTransfer depositAccountTransfer);
    List<DepositAccount> toDepositAccountList(List<DepositAccountTransfer> depositAccountTransferList);
}
