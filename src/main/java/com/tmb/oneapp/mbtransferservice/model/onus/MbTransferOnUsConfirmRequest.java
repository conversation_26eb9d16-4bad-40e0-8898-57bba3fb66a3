package com.tmb.oneapp.mbtransferservice.model.onus;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.mbtransferservice.model.CustomSlip;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MbTransferOnUsConfirmRequest {
    @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
    private String transId;
    private String frUuid;
    private CustomSlip customSlip;
}
