package com.tmb.oneapp.mbtransferservice.model.promptpay;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.mbtransferservice.model.BaseTransferRequest;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@NoArgsConstructor
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TransferPromptpayValidateRequest extends BaseTransferRequest {

  private DepositAccount depositAccount;
}
