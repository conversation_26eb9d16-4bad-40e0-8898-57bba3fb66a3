package com.tmb.oneapp.mbtransferservice.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AccountSaving {
    private boolean cacheFlag;
    private List<DepositAccount> depositAccountLists;
    private List<DepositAccount> fcdAccountLists;
    private List<ProductGroupFlag> productGroupFlag;
    private List<String> mutualFundAccounts;
    private List<LoanAccount> loanAccounts;
    private List<LoanAccount> hpAccounts;
}
