package com.tmb.oneapp.mbtransferservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferRequest;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferResponse;
import com.tmb.oneapp.mbtransferservice.service.AccountTransferService;
import com.tmb.oneapp.mbtransferservice.utils.TransferServiceUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/accounts")
@RequiredArgsConstructor
@Tag(name = "MB Transfer Service")
public class AccountTransferController {
    private static final TMBLogger<AccountTransferController> logger = new TMBLogger<>(AccountTransferController.class);
    private final AccountTransferService accountTransferService;

    @Schema(description = " account transfer")
    @LogAround
    @GetMapping(value = "/transfer")
    public ResponseEntity<TmbOneServiceResponse<List<DepositAccountTransfer>>> accountTransfer(
            @Parameter(name = "CRM ID", example = "001100000000000000000002184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID, required = false) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a79-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "Device ID", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_ID) String deviceId,
            @Parameter(name = "App Version", required = true) @RequestHeader(name = CommonConstant.HEADER_APP_VERSION) String appVersion,
            @Parameter(name = "Device Model", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_MODEL) String deviceModel,
            @Parameter(name = "Timestamp", required = true) @RequestHeader(name = CommonConstant.HEADER_TIMESTAMP) String timestamp,
            @RequestHeader HttpHeaders httpHeaders) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<List<DepositAccountTransfer>> response = new TmbOneServiceResponse<>();
        try {
            List<DepositAccountTransfer> depositAccountTransferList = accountTransferService.getDepositAccountList(correlationId, crmId, false, true);

            response.setData(depositAccountTransferList);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

            logger.info("/accounts/transfer method end Time : {} ", System.currentTimeMillis());

        } catch (Exception e) {
            logger.error("/accounts/transfer error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(response);
    }

    @Schema(description = " Get available balance of account transfer")
    @LogAround
    @PostMapping(value = "/available-balance")
    public ResponseEntity<TmbOneServiceResponse<AccountBalanceTransferResponse>> getAccountBalanceTransfer(
            @Parameter(name = "CRM ID", example = "001100000000000000000002184383", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID, required = false) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a79-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "Device ID", required = true)  @RequestHeader(name = CommonConstant.HEADER_DEVICE_ID) String deviceId,
            @Parameter(name = "App Version", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_APP_VERSION) String appVersion,
            @Parameter(name = "Device Model", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_MODEL) String deviceModel,
            @Parameter(name = "Timestamp", required = true) @RequestHeader(name = CommonConstant.HEADER_TIMESTAMP) String timestamp,
            @RequestHeader HttpHeaders headers,
            @RequestBody @Valid AccountBalanceTransferRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<AccountBalanceTransferResponse> response = new TmbOneServiceResponse<>();
        try {
            AccountBalanceTransferResponse data = accountTransferService.getAccountBalance(request.getAccountNumber(), headers);

            response.setData(data);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

            logger.info("/accounts/available-balance method end Time : {} ", System.currentTimeMillis());

        } catch (Exception e) {
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(response);
    }
}
