package com.tmb.oneapp.mbtransferservice.controller.v1;

import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.offus.MbTransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.service.TransferService;
import com.tmb.oneapp.mbtransferservice.utils.TransferServiceUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;

import jakarta.validation.Valid;

@RestController
@RequiredArgsConstructor
@Tag(name = "MB Transfer Service")
public class TransferController {
    private static final TMBLogger<TransferController> logger = new TMBLogger<>(TransferController.class);
    private final TransferService transferService;

    @Schema(description = "validate other bank data before confirm")
    @LogAround
    @PostMapping(value = "/off-us/validate")
    public ResponseEntity<TmbOneServiceResponse<TransferOffUsValidateResponse>> offUsValidate(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody @Valid MbTransferOffUsValidateRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TransferOffUsValidateResponse> validateResponse = new TmbOneServiceResponse<>();
        try {
            validateResponse = transferService.offUsValidate(correlationId, crmId, httpHeaders, request);
            validateResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
            return ResponseEntity.ok().body(validateResponse);
        } catch (Exception e) {
            logger.error("off-us validate error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(validateResponse);
    }

    @Schema(description = "confirm transfer other bank")
    @LogAround
    @PostMapping(value = "/off-us/confirm")
    public ResponseEntity<TmbOneServiceResponse<TransferOffUsConfirmResponse>> offUsConfirm(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID, required = false) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "device ID", example = "", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_DEVICE_ID) String deviceId,
            @Parameter(name = "App-Version", example = "", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_APP_VERSION) String appVersion,
            @Parameter(name = "Device-Model", example = "", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_DEVICE_MODEL) String deviceModel,
            @Parameter(name = "Timestamp", example = "", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_TIMESTAMP) String timestamp,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody TransferOffUsConfirmRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TransferOffUsConfirmResponse> confirmResponse = new TmbOneServiceResponse<>();
        try {
            confirmResponse = transferService.offUsConfirm(correlationId, crmId, deviceId, appVersion, deviceModel, timestamp, httpHeaders, request);
            confirmResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
            return ResponseEntity.ok().body(confirmResponse);
        } catch (Exception e) {
            logger.error("off-us confirm error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(confirmResponse);
    }

    @Schema(description = "validate promptpay transfer before comfirm")
    @LogAround
    @PostMapping(value = "/promptpay/validate")
    public ResponseEntity<TmbOneServiceResponse<MbTransferPromptpayValidateResponse>> promptpayValidate(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383") @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID, required = false) String crmId,
            @Parameter(name = "Device ID", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_ID) String deviceId,
            @Parameter(name = "Pre Login") @RequestHeader(name = CommonConstant.HEADER_PRE_LOGIN, required = false) String preLogin,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody @Valid MbTransferPromptpayValidateRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<MbTransferPromptpayValidateResponse> response = new TmbOneServiceResponse<>();
        try {
            if(Strings.isNullOrEmpty(crmId)){
                preLogin = "true";
            }
            MbTransferPromptpayValidateResponse validateResponse = transferService.promptpayValidate(correlationId, crmId, deviceId, preLogin, httpHeaders, request);

            if (validateResponse == null) {
                response.setStatus(new TmbStatus(ResponseCode.INVALID_REQUEST.getCode(), ResponseCode.INVALID_REQUEST.getMessage(), ResponseCode.INVALID_REQUEST.getService(), ResponseCode.INVALID_REQUEST.getMessage()));
                return ResponseEntity.badRequest().body(response);
            } else {
                response.setData(validateResponse);

                response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

                logger.info("/promptpay/validate method end Time : {} ", System.currentTimeMillis());
                return ResponseEntity.ok().body(response);

            }
        } catch (Exception e) {
            logger.error("promptpay validate error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(response);
    }

    @Schema(description = "validate internal account before confirmation")
    @LogAround
    @PostMapping(value = "/on-us/validate")
    public ResponseEntity<TmbOneServiceResponse<TransferOnUsValidateResponse>> onUsValidate(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody @Valid MbTransferOnUsValidateRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TransferOnUsValidateResponse> validateResponse = new TmbOneServiceResponse<>();
        try {
            validateResponse = transferService.onUsValidate(correlationId, crmId, httpHeaders, request);
            validateResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
            return ResponseEntity.ok().body(validateResponse);
        } catch (Exception e) {
            logger.error("on-us validate error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(validateResponse);
    }

    @Schema(description = "confirm transfer internal bank")
    @LogAround
    @PostMapping(value = "/on-us/confirm")
    public ResponseEntity<TmbOneServiceResponse<TransferOnUsConfirmResponse>> onUsConfirm(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "device ID", example = "", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_ID) String ignoredDeviceID,
            @Parameter(name = "App-Version", example = "", required = true) @RequestHeader(name = CommonConstant.HEADER_APP_VERSION) String ignoredAppVersion,
            @Parameter(name = "Device-Model", example = "", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_MODEL) String ignoredDeviceModel,
            @Parameter(name = "Timestamp", example = "", required = true) @RequestHeader(name = CommonConstant.HEADER_TIMESTAMP) String ignoredTimestamp,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody MbTransferOnUsConfirmRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<TransferOnUsConfirmResponse> confirmResponse = new TmbOneServiceResponse<>();
        try {
            confirmResponse = transferService.onUsConfirm(correlationId, crmId, httpHeaders, request);
            confirmResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));
            return ResponseEntity.ok().body(confirmResponse);
        } catch (Exception e) {
            logger.error("on-us confirm error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(confirmResponse);
    }


    @Schema(description = " promptpay transfer comfirm")
    @LogAround
    @PostMapping(value = "/promptpay/confirm")
    public ResponseEntity<TmbOneServiceResponse<MbTransferPromptpayConfirmResponse>> promptpayConfirm(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID, required = false) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "Device ID", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_ID) String deviceId,
            @Parameter(name = "App Version", required = true) @RequestHeader(name = CommonConstant.HEADER_APP_VERSION) String appVersion,
            @Parameter(name = "Device Model", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_MODEL) String deviceModel,
            @Parameter(name = "Timestamp", required = true) @RequestHeader(name = CommonConstant.HEADER_TIMESTAMP) String timestamp,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody MbTransferPromptpayConfirmRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<MbTransferPromptpayConfirmResponse> response = new TmbOneServiceResponse<>();
        try {
            MbTransferPromptpayConfirmResponse confirmResponse = transferService.promptpayConfirm(correlationId, crmId, deviceId, appVersion, deviceModel, timestamp, httpHeaders, request);

            if (confirmResponse == null) {
                response.setStatus(new TmbStatus(ResponseCode.INVALID_REQUEST.getCode(), ResponseCode.INVALID_REQUEST.getMessage(), ResponseCode.INVALID_REQUEST.getService(), ResponseCode.INVALID_REQUEST.getMessage()));
                return ResponseEntity.badRequest().body(response);
            } else {
                response.setData(confirmResponse);

                response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), ResponseCode.SUCCESS.getMessage()));

                logger.info("/promptpay/confirm method end Time : {} ", System.currentTimeMillis());
                return ResponseEntity.ok().body(response);

            }
        } catch (Exception e) {
            logger.error("promptpay confirm error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(response);
    }
}
