package com.tmb.oneapp.mbtransferservice.controller.v3;

import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.ServiceHourResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.MbTransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.service.v3.V3TransferService;
import com.tmb.oneapp.mbtransferservice.utils.TransferServiceUtils;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v3")
public class V3TransferController {
    private static final TMBLogger<V3TransferController> logger = new TMBLogger<>(V3TransferController.class);
    private final V3TransferService v3TransferService;

    public V3TransferController(V3TransferService v3TransferService) {
        this.v3TransferService = v3TransferService;
    }

    @Schema(description = "validate other bank data before confirm")
    @LogAround
    @PostMapping(value = "/off-us/validate")
    public ResponseEntity<TmbServiceResponse<TransferOffUsValidateResponse>> offUsValidate(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "Content Signature Pinfree", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CONTENT_SIGNATURE_PINFREE) String contentSignaturePinfree,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody @Valid MbTransferOffUsValidateRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbServiceResponse<TransferOffUsValidateResponse> validateResponse = new TmbServiceResponse<>();
        try {
            validateResponse = v3TransferService.offUsValidate(correlationId, crmId, contentSignaturePinfree, httpHeaders, request);
            validateResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));
            return ResponseEntity.ok().body(validateResponse);
        } catch (Exception e) {
            logger.error("v3 off-us validate error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(validateResponse);
    }

    @Schema(description = "confirm transfer other bank")
    @LogAround
    @PostMapping(value = "/off-us/confirm")
    public ResponseEntity<TmbServiceResponse<TransferOffUsConfirmResponse>> offUsConfirm(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID, required = false) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "device ID", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_DEVICE_ID) String deviceId,
            @Parameter(name = "App-Version", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_APP_VERSION) String ignoredAppVersion,
            @Parameter(name = "Device-Model", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_DEVICE_MODEL) String ignoredDeviceModel,
            @Parameter(name = "Timestamp", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_TIMESTAMP) String timestamp,
            @Parameter(name = "Content Signature Pinfree", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CONTENT_SIGNATURE_PINFREE) String contentSignaturePinfree,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody TransferOffUsConfirmRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbServiceResponse<TransferOffUsConfirmResponse> confirmResponse = new TmbServiceResponse<>();
        try {
            confirmResponse = v3TransferService.offUsConfirm(correlationId, crmId, deviceId, timestamp, contentSignaturePinfree, httpHeaders, request);
            confirmResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));
            return ResponseEntity.ok().body(confirmResponse);
        } catch (Exception e) {
            logger.error("v3 off-us confirm error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(confirmResponse);
    }

    @Schema(description = "validate promptpay transfer before comfirm")
    @LogAround
    @PostMapping(value = "/promptpay/validate")
    public ResponseEntity<TmbServiceResponse<MbTransferPromptpayValidateResponse>> promptpayValidate(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383") @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID, required = false) String crmId,
            @Parameter(name = "Device ID", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_ID) String deviceId,
            @Parameter(name = "Pre Login") @RequestHeader(name = CommonConstant.HEADER_PRE_LOGIN, required = false) String preLogin,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "Content Signature Pinfree", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CONTENT_SIGNATURE_PINFREE) String contentSignaturePinfree,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody @Valid MbTransferPromptpayValidateRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbServiceResponse<MbTransferPromptpayValidateResponse> response = new TmbServiceResponse<>();
        try {
            boolean isPreLoginEmpty = Strings.isNullOrEmpty(crmId);
            if(isPreLoginEmpty){
                preLogin = "true";
            }
            MbTransferPromptpayValidateResponse validateResponse = v3TransferService.promptpayValidate(correlationId, crmId, deviceId, preLogin, contentSignaturePinfree, httpHeaders, request);

            boolean isValidateResponseNull = validateResponse == null;
            if (isValidateResponseNull) {
                response.setStatus(new Status(ResponseCode.INVALID_REQUEST.getCode(), ResponseCode.INVALID_REQUEST.getMessage(), ResponseCode.INVALID_REQUEST.getService(), null));
                return ResponseEntity.badRequest().body(response);
            } else {
                response.setData(validateResponse);

                response.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));

                logger.debug("v3 /promptpay/validate method end Time : {} ", System.currentTimeMillis());
                return ResponseEntity.ok().body(response);

            }
        } catch (Exception e) {
            logger.error("v3 promptpay validate error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(response);
    }

    @Schema(description = "validate internal account before confirmation")
    @LogAround
    @PostMapping(value = "/on-us/validate")
    public ResponseEntity<TmbServiceResponse<TransferOnUsValidateResponse>> onUsValidate(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "Content Signature Pinfree", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CONTENT_SIGNATURE_PINFREE) String contentSignaturePinfree,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody @Valid MbTransferOnUsValidateRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbServiceResponse<TransferOnUsValidateResponse> validateResponse = new TmbServiceResponse<>();
        try {
            validateResponse = v3TransferService.onUsValidate(correlationId, crmId, contentSignaturePinfree, httpHeaders, request);
            validateResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));
            return ResponseEntity.ok().body(validateResponse);
        } catch (Exception e) {
            logger.error("v3 on-us validate error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(validateResponse);
    }

    @Schema(description = "confirm transfer internal bank")
    @LogAround
    @PostMapping(value = "/on-us/confirm")
    public ResponseEntity<TmbServiceResponse<TransferOnUsConfirmResponse>> onUsConfirm(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "device ID", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_ID) String ignoredDeviceID,
            @Parameter(name = "App-Version", required = true) @RequestHeader(name = CommonConstant.HEADER_APP_VERSION) String ignoredAppVersion,
            @Parameter(name = "Device-Model", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_MODEL) String ignoredDeviceModel,
            @Parameter(name = "Timestamp", required = true) @RequestHeader(name = CommonConstant.HEADER_TIMESTAMP) String ignoredTimestamp,
            @Parameter(name = "Content Signature Pinfree", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CONTENT_SIGNATURE_PINFREE) String contentSignaturePinfree,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody MbTransferOnUsConfirmRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbServiceResponse<TransferOnUsConfirmResponse> confirmResponse = new TmbServiceResponse<>();
        try {
            confirmResponse = v3TransferService.onUsConfirm(correlationId, crmId, contentSignaturePinfree, httpHeaders, request);
            confirmResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));
            return ResponseEntity.ok().body(confirmResponse);
        } catch (Exception e) {
            logger.error("v3 on-us confirm error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(confirmResponse);
    }

    @Schema(description = " promptpay transfer comfirm")
    @LogAround
    @PostMapping(value = "/promptpay/confirm")
    public ResponseEntity<TmbServiceResponse<MbTransferPromptpayConfirmResponse>> promptpayConfirm(
            @Parameter(name = "CRM ID", example = "001100000000000000000001184383", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CRM_ID, required = false) String crmId,
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true) @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "Device ID", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_ID) String deviceId,
            @Parameter(name = "App Version", required = true) @RequestHeader(name = CommonConstant.HEADER_APP_VERSION) String ignoredAppVersion,
            @Parameter(name = "Device Model", required = true) @RequestHeader(name = CommonConstant.HEADER_DEVICE_MODEL) String ignoredDeviceModel,
            @Parameter(name = "Timestamp", required = true) @RequestHeader(name = CommonConstant.HEADER_TIMESTAMP) String timestamp,
            @Parameter(name = "Content Signature Pinfree", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CONTENT_SIGNATURE_PINFREE) String contentSignaturePinfree,
            @RequestHeader HttpHeaders httpHeaders,
            @RequestBody MbTransferPromptpayConfirmRequest request) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbServiceResponse<MbTransferPromptpayConfirmResponse> response = new TmbServiceResponse<>();
        try {
            MbTransferPromptpayConfirmResponse confirmResponse = v3TransferService.promptpayConfirm(correlationId, crmId, deviceId, timestamp, contentSignaturePinfree, httpHeaders, request);

            boolean isConfirmResponseNull = confirmResponse == null;
            if (isConfirmResponseNull) {
                response.setStatus(new Status(ResponseCode.INVALID_REQUEST.getCode(), ResponseCode.INVALID_REQUEST.getMessage(), ResponseCode.INVALID_REQUEST.getService(), null));
                return ResponseEntity.badRequest().body(response);
            } else {
                response.setData(confirmResponse);

                response.setStatus(new Status(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), null));

                logger.debug("v3 /promptpay/confirm method end Time : {} ", System.currentTimeMillis());
                return ResponseEntity.ok().body(response);

            }
        } catch (Exception e) {
            logger.error("v3 promptpay confirm error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(response);
    }

    @GetMapping(value = "/check-td-withdraw-cut-off-time")
    public ResponseEntity<TmbOneServiceResponse<ServiceHourResponse>> checkTdWithdrawCutOffTime(
            @Parameter(name = "Correlation ID", example = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da", required = true)
            @Valid @RequestHeader(name = CommonConstant.HEADER_X_CORRELATION_ID) String correlationId,
            @Parameter(name = "Content Signature Pinfree", required = true) @RequestHeader(name = CommonConstant.HEADER_X_CONTENT_SIGNATURE_PINFREE) String contentSignaturePinfree
    ) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        TmbOneServiceResponse<ServiceHourResponse> response = new TmbOneServiceResponse<>();
        try {
            ServiceHourResponse serviceHourResponse =
                    v3TransferService.validateTdCutoffTime(correlationId, contentSignaturePinfree);
            response.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(),
                    ResponseCode.SUCCESS.getService(), null));
            response.setData(serviceHourResponse);
        } catch (Exception e) {
            logger.error("v3 checkTdWithdrawCutOffTime error : {}", e);
            TransferServiceUtils.handleException(e);
        }
        return ResponseEntity.ok().body(response);
    }
}
