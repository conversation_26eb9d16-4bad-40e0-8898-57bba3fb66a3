package com.tmb.oneapp.mbtransferservice.configuration;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerTypePredicate;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Override
    public void configurePathMatch(PathMatchConfigurer configure) {
        configure.addPathPrefix("/v1/mb-transfer-service",  HandlerTypePredicate.forBasePackage("com.tmb.oneapp.mbtransferservice.controller.v1"));
        configure.addPathPrefix("/v2/mb-transfer-service",  HandlerTypePredicate.forBasePackage("com.tmb.oneapp.mbtransferservice.controller.v2"));
    }
}
