package com.tmb.oneapp.mbtransferservice.utils;

import com.fasterxml.jackson.core.Version;
import com.fasterxml.jackson.core.type.TypeReference;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithoutMappingPhrases;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import feign.FeignException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.Callable;

public class TransferServiceUtils {

    private TransferServiceUtils(){}
    private static final String SERVICE_NAME = "mb-transfer-service";
    private static final String PROMPTPAY_NOT_REGISTER = "ppgw_b247048";
    private static final String PROMPTPAY_LINKED = "promptpay_linked";
    private static final String CIRCUIT_BREAKER = "0014";

    public static <T> ResponseEntity<TmbOneServiceResponse<T>> handleGenericResponse(Callable<ResponseEntity<TmbServiceResponse<T>>> callable) throws Exception {
        try{
            ResponseEntity<TmbServiceResponse<T>> response = callable.call();
            Status tmbStatus = Optional.ofNullable(response.getBody()).map(TmbServiceResponse::getStatus).orElse(new Status(ResponseCode.FAILED.getCode(),ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), null));
            if(ResponseCode.SUCCESS.getCode().equals(tmbStatus.getCode())){
                TmbOneServiceResponse<T> inst = new TmbOneServiceResponse<>();
                inst.setData(Optional.ofNullable(response.getBody()).map(TmbServiceResponse::getData).orElse(null));
                inst.setStatus(new TmbStatus(tmbStatus.getCode(),tmbStatus.getMessage(), SERVICE_NAME,  null));
                return new ResponseEntity<>(inst,response.getStatusCode());
            }else{
                boolean isSpecificError = isSpecificErrorWithOutMappingPhrases(tmbStatus);
                if (isSpecificError) {
                    throw new TMBCommonExceptionWithoutMappingPhrases(tmbStatus.getCode(), tmbStatus.getMessage(), SERVICE_NAME, HttpStatus.OK, null, tmbStatus.getDescription());
                }

                throw TransferServiceUtils.failException(tmbStatus.getCode(),tmbStatus.getMessage(), SERVICE_NAME, HttpStatus.valueOf(response.getStatusCode().value()));
            }
        }catch (FeignException ex){
            String content = ex.contentUTF8();
            if(!content.isEmpty()){
                TmbServiceResponse<Object> res = TMBUtils.convertStringToJavaObjWithTypeReference(content, new TypeReference<>() {});
                throw TransferServiceUtils.failException(res.getStatus().getCode(),res.getStatus().getMessage(), SERVICE_NAME, HttpStatus.valueOf(ex.status()));
            }
            throw ex;
        }
    }

    public static <T> ResponseEntity<TmbServiceResponse<T>> handleGenericResponseTmbService(Callable<ResponseEntity<TmbServiceResponse<T>>> callable) throws Exception {
        try{
            ResponseEntity<TmbServiceResponse<T>> response = callable.call();
            Status status = Optional.ofNullable(response.getBody()).map(TmbServiceResponse::getStatus).orElse(new Status(ResponseCode.FAILED.getCode(),ResponseCode.FAILED.getMessage(), ResponseCode.FAILED.getService(), null));
            if(ResponseCode.SUCCESS.getCode().equals(status.getCode())){
                TmbServiceResponse<T> instance = new TmbServiceResponse<>();
                instance.setStatus(new Status(status.getCode(),status.getMessage(), SERVICE_NAME,  null));
                instance.setData(Optional.ofNullable(response.getBody()).map(TmbServiceResponse::getData).orElse(null));
                return new ResponseEntity<>(instance,response.getStatusCode());
            }else{
                boolean isSpecificError = isSpecificErrorWithOutMappingPhrases(status);
                if (isSpecificError) {
                    throw new TMBCommonExceptionWithoutMappingPhrases(status.getCode(), status.getMessage(), SERVICE_NAME, HttpStatus.OK, null, status.getDescription());
                }

                throw TransferServiceUtils.failException(status.getCode(),status.getMessage(), SERVICE_NAME, HttpStatus.valueOf(response.getStatusCode().value()));
            }
        }catch (FeignException ex){
            String contents = ex.contentUTF8();
            if(!contents.isEmpty()){
                TmbServiceResponse<Object> res = TMBUtils.convertStringToJavaObjWithTypeReference(contents, new TypeReference<>() {});
                throw TransferServiceUtils.failException(res.getStatus().getCode(),res.getStatus().getMessage(), SERVICE_NAME, HttpStatus.valueOf(ex.status()));
            }
            throw ex;
        }
    }

    private static boolean isSpecificErrorWithOutMappingPhrases(Status tmbStatus) {
        List<String> listSpecificErrorWithoutMappingPhrase = List.of(
                PROMPTPAY_NOT_REGISTER,
                PROMPTPAY_LINKED,
                CIRCUIT_BREAKER
        );

        return listSpecificErrorWithoutMappingPhrase.contains(tmbStatus.getCode());
    }

    public static void handleException(Exception e) throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        if (e instanceof TMBCommonException) {
            throw (TMBCommonException) e;
        } else if (e instanceof TMBCustomCommonExceptionWithResponse) {
            throw (TMBCustomCommonExceptionWithResponse) e;
        } else if (e.getCause() instanceof TMBCommonException) {
            throw (TMBCommonException) e.getCause();
        } else if (e.getCause() instanceof TMBCustomCommonExceptionWithResponse) {
            throw (TMBCustomCommonExceptionWithResponse) e.getCause();
        } else {
            throw failException(ResponseCode.FAILED);
        }
    }

    private static TMBCommonException failException(String code,String message,String service,HttpStatus status){
        return new TMBCommonException(
                code,
                message,
                service,
                status,
                null);
    }
    public static TMBCommonException failException(ResponseCode responseCode) {
        return failException(responseCode.getCode(),responseCode.getMessage(),responseCode.getService(),HttpStatus.OK);
    }

    public static TMBCommonException getTMBCommonException(ResponseCode responseCode, String message) {
        return new TMBCommonException(responseCode.getCode(), message, responseCode.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR, null);
    }

    public static TMBCommonException getTMBCommonException(ResponseCode responseCode, HttpStatus httpStatus) {
        return new TMBCommonException(responseCode.getCode(), responseCode.getMessage(), responseCode.getService(),
                httpStatus, null);
    }

    public static boolean isFcdAccount(String accountNo) {
        return "8".equals(StringUtils.substring(StringUtils.right(accountNo, 10), 3, 4));
    }

    public static Version getVersion(String versionString) {
        int index = versionString.indexOf("-");
        if (index >= 0) {
            versionString = versionString.substring(0, index);
        }
        int[] versionSplit = Arrays.stream(versionString.split("\\.")).mapToInt(Integer::parseInt).toArray();
        int major = versionSplit[0];
        int minor = versionSplit.length > 1 ? versionSplit[1] : 0;
        int patch = versionSplit.length > 2 ? versionSplit[2] : 0;
        return new Version(major, minor, patch, null, null, null);
    }
}
