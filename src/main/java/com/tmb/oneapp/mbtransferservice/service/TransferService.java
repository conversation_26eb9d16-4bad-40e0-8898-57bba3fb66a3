package com.tmb.oneapp.mbtransferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.MbTransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateResponse;
import org.springframework.http.HttpHeaders;

import java.util.List;

public interface TransferService {
    TmbOneServiceResponse<TransferOffUsValidateResponse> offUsValidate(String correlationId, String crmId, HttpHeaders header, MbTransferOffUsValidateRequest request) throws TMBCommonException;
    TmbOneServiceResponse<TransferOffUsConfirmResponse> offUsConfirm(String correlationId,
                                                                     String crmId,
                                                                     String deviceId,
                                                                     String appVersion,
                                                                     String deviceModel,
                                                                     String timestamp,
                                                                     HttpHeaders httpHeaders,
                                                                     TransferOffUsConfirmRequest request) throws TMBCommonException;
    MbTransferPromptpayValidateResponse promptpayValidate(String correlationId,
                                                          String xcrmId,
                                                          String deviceId,
                                                          String preLogin,
                                                          HttpHeaders httpHeaders,
                                                          MbTransferPromptpayValidateRequest request) throws TMBCommonException;

    TmbOneServiceResponse<TransferOnUsConfirmResponse> onUsConfirm(String correlationId,
                                                                   String crmId,
                                                                   HttpHeaders httpHeaders,
                                                                   MbTransferOnUsConfirmRequest request) throws TMBCommonException;

    TmbOneServiceResponse<TransferOnUsValidateResponse> onUsValidate(String correlationId, String crmId, HttpHeaders header, MbTransferOnUsValidateRequest request) throws TMBCommonException;



    MbTransferPromptpayConfirmResponse promptpayConfirm(String correlationId,
                                                        String crmId,
                                                        String deviceId,
                                                        String appVersion,
                                                        String deviceModel,
                                                        String timestamp,
                                                        HttpHeaders httpHeaders,
                                                        MbTransferPromptpayConfirmRequest request) throws TMBCommonException;
}
