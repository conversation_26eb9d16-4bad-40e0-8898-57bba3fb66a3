package com.tmb.oneapp.mbtransferservice.service.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.mbtransferservice.model.V2DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferResponse;
import org.springframework.http.HttpHeaders;

public interface V2AccountTransferService {

    V2DepositAccountTransfer getDepositAccountList(String correlationId, String crmId, Boolean refreshFlag, Boolean allAccountFlag, String appVersion) throws TMBCommonException;

    AccountBalanceTransferResponse getAccountBalance(String accountNumber, String accountType, String financialId, HttpHeaders httpHeaders) throws TMBCommonException;
}
