package com.tmb.oneapp.mbtransferservice.service.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.mbtransferservice.model.ServiceHourResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.MbTransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateResponse;
import org.springframework.http.HttpHeaders;

public interface V2TransferService {
    TmbServiceResponse<TransferOnUsConfirmResponse> onUsConfirm(String correlationId,
                                                                String crmId,
                                                                HttpHeaders httpHeaders,
                                                                MbTransferOnUsConfirmRequest request) throws TMBCommonException;

    TmbServiceResponse<TransferOnUsValidateResponse> onUsValidate(String correlationId, String crmId, HttpHeaders header, MbTransferOnUsValidateRequest request) throws TMBCommonException;
    TmbServiceResponse<TransferOffUsConfirmResponse> offUsConfirm(String correlationId,
                                                                     String crmId,
                                                                     String deviceId,
                                                                     String timestamp,
                                                                     HttpHeaders httpHeaders,
                                                                     TransferOffUsConfirmRequest request) throws TMBCommonException;
    TmbServiceResponse<TransferOffUsValidateResponse> offUsValidate(String correlationId, String crmId, HttpHeaders header, MbTransferOffUsValidateRequest request) throws TMBCommonException;

    MbTransferPromptpayConfirmResponse promptpayConfirm(String correlationId,
                                                        String crmId,
                                                        String deviceId,
                                                        String timestamp,
                                                        HttpHeaders httpHeaders,
                                                        MbTransferPromptpayConfirmRequest request) throws TMBCommonException;

    MbTransferPromptpayValidateResponse promptpayValidate(String correlationId,
                                                          String xcrmId,
                                                          String deviceId,
                                                          String preLogin,
                                                          HttpHeaders httpHeaders,
                                                          MbTransferPromptpayValidateRequest request) throws TMBCommonException;

    ServiceHourResponse validateTdCutoffTime(String correlationId) throws TMBCommonException;
}
