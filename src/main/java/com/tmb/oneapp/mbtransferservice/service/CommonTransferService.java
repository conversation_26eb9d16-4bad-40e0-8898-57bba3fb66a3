package com.tmb.oneapp.mbtransferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;

import java.math.BigDecimal;
import java.util.Date;

public interface CommonTransferService {
    DepositAccount validateAccountTransfer(String crmId,String correlationId,String fromAccountNo) throws TMBCommonException;
    Boolean isOverAvailableBalance(BigDecimal availableAmount, String amount) throws TMBCommonException;
    String getCrmIdFromDeviceId(String crmId,String deviceId) throws TMBCommonException;
    DepositAccount validateAccountTransfer(String crmId, String correlationId, MbTransferOnUsValidateRequest request) throws TMBCommonException;
    Boolean isNonWorkingHour(String nonServiceHourStart, String nonServiceHourEnd, Date currentDate);
}
