package com.tmb.oneapp.mbtransferservice.service.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.mbtransferservice.client.TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.client.v2.V2TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.CommonTime;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.ServiceHourResponse;
import com.tmb.oneapp.mbtransferservice.model.TransferModuleModel;
import com.tmb.oneapp.mbtransferservice.model.offus.MbTransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.service.CommonTransferService;
import com.tmb.oneapp.mbtransferservice.utils.TransferServiceUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.ACCOUNT_TYPE_CDA;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_DEVICE_ID;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_DEVICE_MODEL;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_IP_ADDRESS;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.TRANSFER_ERROR_TTB_CBS_PREFIX;
import static com.tmb.oneapp.mbtransferservice.constant.ResponseCode.INSUFFICIENT_FUNDS;
import static com.tmb.oneapp.mbtransferservice.constant.ResponseCode.INVALID_ACCOUNT_STATUS;
import static com.tmb.oneapp.mbtransferservice.constant.ResponseCode.UNAVAILABLE_SERVICE_HOUR;

@Service
@RequiredArgsConstructor
public class V2TransferServiceImpl implements V2TransferService {
    private static final TMBLogger<V2TransferServiceImpl> logger = new TMBLogger<>(V2TransferServiceImpl.class);

    private final V2TransferServiceClient transferServiceClient;
    private final TransferServiceClient transferClient;
    private final CommonTransferService commonTransferService;

    @Override
    public TmbServiceResponse<TransferOffUsValidateResponse> offUsValidate(String correlationId, String crmId, HttpHeaders httpHeaders, MbTransferOffUsValidateRequest request) throws TMBCommonException {

        DepositAccount depositAccount = validateAccountAndBalance(correlationId, crmId, request.getFromAccountNo(), request.getAmount());

        TransferOffUsValidateRequest offUsValidateRequest = new TransferOffUsValidateRequest();
        offUsValidateRequest.setFromAccountNo(request.getFromAccountNo());
        offUsValidateRequest.setToAccountNo(request.getToAccountNo());
        offUsValidateRequest.setToFavoriteName(request.getToFavoriteName());
        offUsValidateRequest.setToBankCode(request.getToBankCode());
        offUsValidateRequest.setCategoryId(request.getCategoryId());
        offUsValidateRequest.setAmount(request.getAmount());
        offUsValidateRequest.setFlow(request.getFlow());
        offUsValidateRequest.setNote(request.getNote());
        offUsValidateRequest.setDepositAccount(depositAccount);
        offUsValidateRequest.setDepositNo(request.getDepositNo());
        try {
            ResponseEntity<TmbServiceResponse<TransferOffUsValidateResponse>> validateResponse = TransferServiceUtils.handleGenericResponseTmbService(() -> transferServiceClient.offUsValidate(correlationId, crmId, httpHeaders.getFirst(HEADER_IP_ADDRESS), httpHeaders.getFirst(HEADER_APP_VERSION), offUsValidateRequest));
            return Optional.ofNullable(validateResponse).map(ResponseEntity::getBody).orElseThrow(()-> TransferServiceUtils.failException(ResponseCode.FAILED));
        } catch(TMBCommonException tex){
            throw tex;
        } catch (Exception e) {
            logger.error("transfer-service off us validate error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
    }

    @Override
    public TmbServiceResponse<TransferOffUsConfirmResponse> offUsConfirm(String correlationId,
                                                                            String crmId,
                                                                            String deviceId,
                                                                            String timestamp,
                                                                            HttpHeaders httpHeaders,
                                                                            TransferOffUsConfirmRequest request) throws TMBCommonException {
        String deviceModel = httpHeaders.getFirst(HEADER_DEVICE_MODEL);
        String appVersion = httpHeaders.getFirst(HEADER_APP_VERSION);
        try {
            ResponseEntity<TmbServiceResponse<TransferOffUsConfirmResponse>> confirmResponse = TransferServiceUtils.handleGenericResponseTmbService(() -> transferServiceClient.offUsConfirm(correlationId, crmId, deviceId,appVersion,deviceModel,timestamp, httpHeaders.getFirst(HEADER_IP_ADDRESS),request));
            return Optional.ofNullable(confirmResponse).map(ResponseEntity::getBody).orElseThrow(()-> TransferServiceUtils.failException(ResponseCode.FAILED));
        } catch(TMBCommonException tex){
            throw tex;
        } catch (Exception e) {
            logger.error("transfer-service off us confirm error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
    }

    private DepositAccount validateAccountAndBalance(String correlationId, String crmId, String fromAccountNo, String amount) throws TMBCommonException {
        DepositAccount depositAccount = commonTransferService.validateAccountTransfer(crmId, correlationId, fromAccountNo);
        boolean isInValid = commonTransferService.isOverAvailableBalance(depositAccount.getAvailableBalance(), amount);
        if (isInValid) {
            logger.debug("validateAccountAndBalance : {}", isInValid);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
        return depositAccount;
    }

    private DepositAccount validateAccountAndBalance(String correlationId,
                                                     String crmId,
                                                     MbTransferOnUsValidateRequest request)
            throws TMBCommonException {
        DepositAccount depositAccount = commonTransferService
                .validateAccountTransfer(crmId, correlationId, request);
        boolean isInValid = commonTransferService
                .isOverAvailableBalance(depositAccount.getAvailableBalance(), request.getAmount());
        if (isInValid) {
            logger.debug("validateAccountAndBalance : true");
            throw TransferServiceUtils.failException(INSUFFICIENT_FUNDS);
        }
        return depositAccount;
    }

    @Override
    public MbTransferPromptpayValidateResponse promptpayValidate(String correlationId,
                                                                 String xcrmId,
                                                                 String deviceId,
                                                                 String preLogin,
                                                                 HttpHeaders httpHeaders,
                                                                 MbTransferPromptpayValidateRequest request) throws TMBCommonException{
        String crmId = commonTransferService.getCrmIdFromDeviceId(xcrmId,deviceId);
        DepositAccount depositAccount = validateAccountAndBalance(correlationId, crmId, request.getFromAccountNo(), request.getAmount());

        TransferPromptpayValidateRequest promptpayValidateRequest = new TransferPromptpayValidateRequest();
        promptpayValidateRequest.setFromAccountNo(request.getFromAccountNo());
        promptpayValidateRequest.setToBankCode(request.getToBankCode());
        promptpayValidateRequest.setToAccountNo(request.getToAccountNo());
        promptpayValidateRequest.setAmount(request.getAmount());
        promptpayValidateRequest.setToFavoriteName(request.getToFavoriteName());
        promptpayValidateRequest.setNote(request.getNote());
        promptpayValidateRequest.setCategoryId(request.getCategoryId());
        promptpayValidateRequest.setQr(request.getQr());
        promptpayValidateRequest.setFlow(request.getFlow());
        promptpayValidateRequest.setDepositAccount(depositAccount);
        promptpayValidateRequest.setDepositNo(request.getDepositNo());

        try {
            ResponseEntity<TmbServiceResponse<TransferPromptpayValidateResponse>> validateResponse = TransferServiceUtils.handleGenericResponseTmbService(()-> transferServiceClient.promptpayValidate(correlationId, crmId, deviceId, preLogin, httpHeaders.getFirst(HEADER_IP_ADDRESS),httpHeaders.getFirst(HEADER_APP_VERSION), promptpayValidateRequest));
            TransferPromptpayValidateResponse body = Optional.ofNullable(validateResponse).map(ResponseEntity::getBody)
                    .map(TmbServiceResponse::getData).orElseThrow(()-> TransferServiceUtils.failException(ResponseCode.FAILED));

            MbTransferPromptpayValidateResponse data = new MbTransferPromptpayValidateResponse();
            data.setFee(body.getFee());
            data.setAmount(body.getAmount());
            data.setToAccountName(body.getToAccountName());
            data.setTransId(body.getTransId());
            data.setIsRequireFr(body.getIsRequireFr());
            data.setIsRequireConfirmPin(body.getIsRequireConfirmPin());
            data.setCommonAuthenticationInformation(body.getCommonAuthenticationInformation());
            data.setIsRequireCommonAuthen(body.getIsRequireCommonAuthen());


            return data;

        } catch(TMBCommonException tex){
            throw tex;
        }catch (Exception e) {
            logger.error("transfer-service promptpay validate error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }

    }

    @Override
    public MbTransferPromptpayConfirmResponse promptpayConfirm(String correlationId,
                                                               String xcrmId,
                                                               String deviceId,
                                                               String timestamp,
                                                               HttpHeaders httpHeaders,
                                                               MbTransferPromptpayConfirmRequest request) throws TMBCommonException{
        try{
            String appVersion = httpHeaders.getFirst(HEADER_APP_VERSION);
            String deviceModel = httpHeaders.getFirst(HEADER_DEVICE_MODEL);
            String crmId = commonTransferService.getCrmIdFromDeviceId(xcrmId,deviceId);
            TransferPromptpayConfirmRequest confirmRequest = new TransferPromptpayConfirmRequest();
            confirmRequest.setFrUuid(request.getFrUuid());
            confirmRequest.setTransId(request.getTransId());
            confirmRequest.setCustomSlip(request.getCustomSlip());
            ResponseEntity<TmbServiceResponse<TransferPromptpayConfirmResponse>> confirmResponse = TransferServiceUtils.handleGenericResponseTmbService(() -> transferServiceClient.promptpayConfirm (correlationId, crmId, deviceId, appVersion, deviceModel, timestamp, httpHeaders.getFirst(HEADER_IP_ADDRESS),confirmRequest));
            TransferPromptpayConfirmResponse body = Optional.ofNullable (confirmResponse).map(ResponseEntity::getBody).map(TmbServiceResponse::getData).orElseThrow(() -> TransferServiceUtils.failException(ResponseCode.FAILED));


            MbTransferPromptpayConfirmResponse data = new MbTransferPromptpayConfirmResponse();
            data.setRemainingBalance(body.getRemainingBalance());
            data.setReferenceNo(body.getReferenceNo());
            data.setIsToOwnAccount(body.getIsToOwnAccount());
            data.setTransferCreatedDatetime(body.getTransferCreatedDatetime());
            data.setQr(body.getQr());
            return data;
        } catch(TMBCommonException tex){
            throw tex;
        } catch (Exception e){
            logger.error("transfer-service v2 promptpay confirm error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }

    }


    @Override
    public TmbServiceResponse<TransferOnUsValidateResponse> onUsValidate(String correlationId,
                                                                         String crmId,
                                                                         HttpHeaders httpHeaders,
                                                                         MbTransferOnUsValidateRequest request)
            throws TMBCommonException {

        if(ACCOUNT_TYPE_CDA.equals(TMBUtils.getAccountType(request.getToAccountNo()))){
            validateTDServiceHour(correlationId);
        }

        DepositAccount depositAccount = validateAccountAndBalance(correlationId, crmId, request);

        TransferOnUsValidateRequest transferOnUsValidateRequest = getTransferOnUsValidateRequest(request, depositAccount);

        try {
            ResponseEntity<TmbServiceResponse<TransferOnUsValidateResponse>> validateResponse = TransferServiceUtils
                    .handleGenericResponseTmbService(()-> transferServiceClient.onUsValidate(correlationId, crmId,
                            httpHeaders.getFirst(HEADER_DEVICE_ID),
                            httpHeaders.getFirst(HEADER_IP_ADDRESS),
                            httpHeaders.getFirst(HEADER_APP_VERSION),
                            transferOnUsValidateRequest));
            return Optional.of(validateResponse).map(ResponseEntity::getBody).orElse(null);
        } catch(TMBCommonException tex){
            throw tex;
        } catch (Exception e) {
            logger.error("transfer-service v2 on us validate error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.CONNECTION_FAIL);
        }
    }

    private static TransferOnUsValidateRequest getTransferOnUsValidateRequest(MbTransferOnUsValidateRequest request,
                                                                              DepositAccount depositAccount) {
        TransferOnUsValidateRequest transferOnUsValidateRequest = new TransferOnUsValidateRequest();
        transferOnUsValidateRequest.setToAccountNo(request.getToAccountNo());
        transferOnUsValidateRequest.setFromAccountNo(request.getFromAccountNo());
        transferOnUsValidateRequest.setAmount(request.getAmount());
        transferOnUsValidateRequest.setBankCode(request.getToFavoriteName());
        transferOnUsValidateRequest.setNote(request.getNote());
        transferOnUsValidateRequest.setCategoryId(request.getCategoryId());
        transferOnUsValidateRequest.setDepositNo(request.getDepositNo());
        transferOnUsValidateRequest.setFlow(request.getFlow());
        transferOnUsValidateRequest.setToFavoriteName(request.getToFavoriteName());
        transferOnUsValidateRequest.setDepositAccount(depositAccount);
        transferOnUsValidateRequest.setFxTransId(request.getFxTransId());
        return transferOnUsValidateRequest;
    }

    @Override
    public TmbServiceResponse<TransferOnUsConfirmResponse> onUsConfirm(String correlationId,
                                                                       String crmId,
                                                                       HttpHeaders httpHeaders,
                                                                       MbTransferOnUsConfirmRequest request)
            throws TMBCommonException {
        try {
            ResponseEntity<TmbServiceResponse<TransferOnUsConfirmResponse>> confirmResponse = TransferServiceUtils
                    .handleGenericResponseTmbService(()-> transferServiceClient.onUsConfirm(correlationId, crmId,
                            httpHeaders.getFirst(HEADER_DEVICE_ID),
                            httpHeaders.getFirst(HEADER_IP_ADDRESS),
                            httpHeaders.getFirst(HEADER_APP_VERSION),
                            request));
            return Optional.of(confirmResponse).map(ResponseEntity::getBody).orElse(null);
        } catch(TMBCommonException tex){
            mapCbsErrorCodeToDsErrorCode(tex);
            throw tex;
        } catch (Exception e) {
            logger.error("transfer-service v2 on us confirm error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.CONNECTION_FAIL);
        }
    }

    private static void mapCbsErrorCodeToDsErrorCode(TMBCommonException e) {
        if(Objects.nonNull(e)){
            if((TRANSFER_ERROR_TTB_CBS_PREFIX + "8005").equals(e.getErrorCode())){
                e.setErrorCode(INSUFFICIENT_FUNDS.getCode());
                e.setErrorMessage(INSUFFICIENT_FUNDS.getMessage());
            } else {
                if((TRANSFER_ERROR_TTB_CBS_PREFIX + "8009").equals(e.getErrorCode()) ||
                        (TRANSFER_ERROR_TTB_CBS_PREFIX + "8004").equals(e.getErrorCode())) {
                    e.setErrorCode(INVALID_ACCOUNT_STATUS.getCode());
                    e.setErrorMessage(INVALID_ACCOUNT_STATUS.getMessage());
                }
            }
        }
    }

    private void validateTDServiceHour(String correlationId) throws TMBCommonException {
        try {
            CommonTime tdCutOffTime = getWithdrawTdCutoffTime(correlationId);

            if (tdCutOffTime != null && commonTransferService
                    .isNonWorkingHour(tdCutOffTime.getEnd(), tdCutOffTime.getStart(), new Date())) {
                throw TransferServiceUtils.failException(UNAVAILABLE_SERVICE_HOUR);
            }

        } catch (Exception e) {
            logger.error("validateTDServiceHour error: {}", e.getMessage(), e);
            throw e;
        }
    }

    public ServiceHourResponse validateTdCutoffTime(String correlationId) throws TMBCommonException {
        try {
            CommonTime tdCutOffTime = getWithdrawTdCutoffTime(correlationId);
            ServiceHourResponse serviceHourResponse = new ServiceHourResponse();
            serviceHourResponse.setNonServiceHour(false);
            serviceHourResponse.setStart(tdCutOffTime.getStart());
            serviceHourResponse.setEnd(tdCutOffTime.getEnd());
            if (commonTransferService.isNonWorkingHour(tdCutOffTime.getEnd(), tdCutOffTime.getStart(), new Date())) {
                serviceHourResponse.setNonServiceHour(true);
            }
            return serviceHourResponse;
        } catch (Exception e) {
            logger.error("validateTdCutoffTime error: {}", e.getMessage(), e);
            throw new TMBCommonException("Cannot get td withdrawal cut off time");
        }
    }

    private CommonTime getWithdrawTdCutoffTime(String correlationId) {
        try {
            ResponseEntity<TmbOneServiceResponse<TransferModuleModel>> responseEntity =
                    transferClient.getAccountConfiguration(correlationId);

            return Optional.of(responseEntity).map(ResponseEntity::getBody)
                    .map(TmbOneServiceResponse::getData)
                    .map(TransferModuleModel::getWithdrawTdCutoffTime)
                    .orElse(null);
        } catch (Exception e) {
            logger.error("getWithdrawTdCutoffTime error: {}", e.getMessage(), e);
            throw e;
        }
    }

}
