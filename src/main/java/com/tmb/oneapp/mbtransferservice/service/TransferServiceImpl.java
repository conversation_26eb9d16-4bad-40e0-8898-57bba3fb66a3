package com.tmb.oneapp.mbtransferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.mbtransferservice.client.TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.offus.MbTransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.utils.TransferServiceUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_DEVICE_ID;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_IP_ADDRESS;

@Service
@RequiredArgsConstructor
public class TransferServiceImpl implements TransferService {

    private static final TMBLogger<TransferServiceImpl> logger = new TMBLogger<>(TransferServiceImpl.class);
    private final CommonTransferService commonTransferService;
    private final TransferServiceClient transferServiceClient;

    @Override
    public TmbOneServiceResponse<TransferOffUsValidateResponse> offUsValidate(String correlationId, String crmId, HttpHeaders httpHeaders, MbTransferOffUsValidateRequest request) throws TMBCommonException {

        DepositAccount depositAccount = validateAccountAndBalance(correlationId, crmId, request.getFromAccountNo(), request.getAmount());

        TransferOffUsValidateRequest offUsValidateRequest = new TransferOffUsValidateRequest();
        offUsValidateRequest.setFromAccountNo(request.getFromAccountNo());
        offUsValidateRequest.setToAccountNo(request.getToAccountNo());
        offUsValidateRequest.setToBankCode(request.getToBankCode());
        offUsValidateRequest.setToFavoriteName(request.getToFavoriteName());
        offUsValidateRequest.setAmount(request.getAmount());
        offUsValidateRequest.setCategoryId(request.getCategoryId());
        offUsValidateRequest.setNote(request.getNote());
        offUsValidateRequest.setFlow(request.getFlow());
        offUsValidateRequest.setDepositNo(request.getDepositNo());
        offUsValidateRequest.setDepositAccount(depositAccount);
        try {
            ResponseEntity<TmbOneServiceResponse<TransferOffUsValidateResponse>> validateResponse = TransferServiceUtils.handleGenericResponse(() -> transferServiceClient.offUsValidate(correlationId, crmId, httpHeaders.getFirst(HEADER_IP_ADDRESS), httpHeaders.getFirst(HEADER_APP_VERSION), offUsValidateRequest));
            return Optional.ofNullable(validateResponse).map(ResponseEntity::getBody).orElseThrow(()-> TransferServiceUtils.failException(ResponseCode.FAILED));
        } catch(TMBCommonException tex){
            throw tex;
        } catch (Exception e) {
            logger.error("transfer-service off us validate error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
    }

    @Override
    public TmbOneServiceResponse<TransferOffUsConfirmResponse> offUsConfirm(String correlationId,
                                                                            String crmId,
                                                                            String deviceId,
                                                                            String appVersion,
                                                                            String deviceModel,
                                                                            String timestamp,
                                                                            HttpHeaders httpHeaders,
                                                                            TransferOffUsConfirmRequest request) throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<TransferOffUsConfirmResponse>> confirmResponse = TransferServiceUtils.handleGenericResponse(() -> transferServiceClient.offUsConfirm(correlationId, crmId, deviceId,appVersion,deviceModel,timestamp, httpHeaders.getFirst(HEADER_IP_ADDRESS),request));
            return Optional.ofNullable(confirmResponse).map(ResponseEntity::getBody).orElseThrow(()-> TransferServiceUtils.failException(ResponseCode.FAILED));
        } catch(TMBCommonException tex){
            throw tex;
        } catch (Exception e) {
            logger.error("transfer-service off us confirm error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
    }

    private DepositAccount validateAccountAndBalance(String correlationId, String crmId, String fromAccountNo, String amount) throws TMBCommonException {
        DepositAccount depositAccount = commonTransferService.validateAccountTransfer(crmId, correlationId, fromAccountNo);
        boolean isInValid = commonTransferService.isOverAvailableBalance(depositAccount.getAvailableBalance(), amount);
        if (isInValid) {
            logger.info("validateAccountAndBalance : {}", isInValid);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
        return depositAccount;
    }

    @Override
    public MbTransferPromptpayValidateResponse promptpayValidate(String correlationId,
                                                                 String xcrmId,
                                                                 String deviceId,
                                                                 String preLogin,
                                                                 HttpHeaders httpHeaders,
                                                                 MbTransferPromptpayValidateRequest request) throws TMBCommonException{
        String crmId = commonTransferService.getCrmIdFromDeviceId(xcrmId,deviceId);
        DepositAccount depositAccount = validateAccountAndBalance(correlationId, crmId, request.getFromAccountNo(), request.getAmount());

        TransferPromptpayValidateRequest promptpayValidateRequest = new TransferPromptpayValidateRequest();
        promptpayValidateRequest.setFromAccountNo(request.getFromAccountNo());
        promptpayValidateRequest.setToAccountNo(request.getToAccountNo());
        promptpayValidateRequest.setToBankCode(request.getToBankCode());
        promptpayValidateRequest.setToFavoriteName(request.getToFavoriteName());
        promptpayValidateRequest.setAmount(request.getAmount());
        promptpayValidateRequest.setCategoryId(request.getCategoryId());
        promptpayValidateRequest.setNote(request.getNote());
        promptpayValidateRequest.setFlow(request.getFlow());
        promptpayValidateRequest.setQr(request.getQr());
        promptpayValidateRequest.setDepositNo(request.getDepositNo());
        promptpayValidateRequest.setDepositAccount(depositAccount);

        try {
            ResponseEntity<TmbOneServiceResponse<TransferPromptpayValidateResponse>> validateResponse = TransferServiceUtils.handleGenericResponse(()-> transferServiceClient.promptpayValidate(correlationId, crmId, deviceId, preLogin, httpHeaders.getFirst(HEADER_IP_ADDRESS),httpHeaders.getFirst(HEADER_APP_VERSION), promptpayValidateRequest));
            TransferPromptpayValidateResponse body = Optional.ofNullable(validateResponse).map(ResponseEntity::getBody)
                    .map(TmbOneServiceResponse::getData).orElseThrow(()-> TransferServiceUtils.failException(ResponseCode.FAILED));

            MbTransferPromptpayValidateResponse data = new MbTransferPromptpayValidateResponse();
            data.setAmount(body.getAmount());
            data.setFee(body.getFee());
            data.setTransId(body.getTransId());
            data.setToAccountName(body.getToAccountName());
            data.setIsRequireConfirmPin(body.getIsRequireConfirmPin());
            data.setIsRequireFr(body.getIsRequireFr());
            data.setIsRequireCommonAuthen(body.getIsRequireCommonAuthen());
            data.setCommonAuthenticationInformation(body.getCommonAuthenticationInformation());


            return data;

        } catch(TMBCommonException tex){
            throw tex;
        }catch (Exception e) {
            logger.error("transfer-service promptpay validate error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }

    }

    @Override
    public MbTransferPromptpayConfirmResponse promptpayConfirm(String correlationId,
                                                        String xcrmId,
                                                        String deviceId,
                                                        String appVersion,
                                                        String deviceModel,
                                                        String timestamp,
                                                        HttpHeaders httpHeaders,
                                                        MbTransferPromptpayConfirmRequest request) throws TMBCommonException{
        try{
            String crmId = commonTransferService.getCrmIdFromDeviceId(xcrmId,deviceId);
            TransferPromptpayConfirmRequest confirmRequest = new TransferPromptpayConfirmRequest();
            confirmRequest.setTransId(request.getTransId());
            confirmRequest.setFrUuid(request.getFrUuid());
            confirmRequest.setCustomSlip(request.getCustomSlip());
            ResponseEntity<TmbOneServiceResponse<TransferPromptpayConfirmResponse>> confirmResponse = TransferServiceUtils.handleGenericResponse(() -> transferServiceClient.promptpayConfirm (correlationId, crmId, deviceId, appVersion, deviceModel, timestamp, httpHeaders.getFirst(HEADER_IP_ADDRESS),confirmRequest));
            TransferPromptpayConfirmResponse body = Optional.ofNullable (confirmResponse).map(ResponseEntity::getBody).map(TmbOneServiceResponse::getData).orElseThrow(() -> TransferServiceUtils.failException(ResponseCode.FAILED));


            MbTransferPromptpayConfirmResponse data = new MbTransferPromptpayConfirmResponse();
            data.setReferenceNo(body.getReferenceNo());
            data.setRemainingBalance(body.getRemainingBalance());
            data.setTransferCreatedDatetime(body.getTransferCreatedDatetime());
            data.setIsToOwnAccount(body.getIsToOwnAccount());
            data.setQr(body.getQr());
            return data;
        } catch(TMBCommonException tex){
            throw tex;
        } catch (Exception e){
            logger.error("transfer-service promptpay confirm error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }

    }

    @Override
    public TmbOneServiceResponse<TransferOnUsValidateResponse> onUsValidate(String correlationId, String crmId,
                                                                            HttpHeaders httpHeaders,
                                                                            MbTransferOnUsValidateRequest request)
            throws TMBCommonException {

        DepositAccount depositAccount = validateAccountAndBalance(correlationId, crmId, request.getFromAccountNo(), request.getAmount());

        TransferOnUsValidateRequest transferOnUsValidateRequest = getTransferOnUsValidateRequest(request, depositAccount);

        try {
            ResponseEntity<TmbOneServiceResponse<TransferOnUsValidateResponse>> validateResponse = TransferServiceUtils
                    .handleGenericResponse(()-> transferServiceClient.onUsValidate(correlationId, crmId,
                            httpHeaders.getFirst(HEADER_DEVICE_ID),
                            httpHeaders.getFirst(HEADER_IP_ADDRESS),
                            httpHeaders.getFirst(HEADER_APP_VERSION),
                            transferOnUsValidateRequest));
            return Optional.of(validateResponse).map(ResponseEntity::getBody).orElse(null);
        } catch(TMBCommonException tex){
            throw tex;
        } catch (Exception e) {
            logger.error("transfer-service on us validate error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
    }

    private static TransferOnUsValidateRequest getTransferOnUsValidateRequest(MbTransferOnUsValidateRequest request,
                                                                              DepositAccount depositAccount) {
        TransferOnUsValidateRequest transferOnUsValidateRequest = new TransferOnUsValidateRequest();
        transferOnUsValidateRequest.setFromAccountNo(request.getFromAccountNo());
        transferOnUsValidateRequest.setToAccountNo(request.getToAccountNo());
        transferOnUsValidateRequest.setBankCode(request.getToFavoriteName());
        transferOnUsValidateRequest.setAmount(request.getAmount());
        transferOnUsValidateRequest.setCategoryId(request.getCategoryId());
        transferOnUsValidateRequest.setNote(request.getNote());
        transferOnUsValidateRequest.setFlow(request.getFlow());
        transferOnUsValidateRequest.setDepositNo(request.getDepositNo());
        transferOnUsValidateRequest.setDepositAccount(depositAccount);
        transferOnUsValidateRequest.setToFavoriteName(request.getToFavoriteName());
        return transferOnUsValidateRequest;
    }

    @Override
    public TmbOneServiceResponse<TransferOnUsConfirmResponse> onUsConfirm(String correlationId, String crmId,
                                                                          HttpHeaders httpHeaders,
                                                                          MbTransferOnUsConfirmRequest request)
            throws TMBCommonException {
        try {
            ResponseEntity<TmbOneServiceResponse<TransferOnUsConfirmResponse>> confirmResponse = TransferServiceUtils
                    .handleGenericResponse(()-> transferServiceClient.onUsConfirm(correlationId, crmId,
                            httpHeaders.getFirst(HEADER_DEVICE_ID),
                            httpHeaders.getFirst(HEADER_IP_ADDRESS),
                            httpHeaders.getFirst(HEADER_APP_VERSION),
                            request));
            return Optional.of(confirmResponse).map(ResponseEntity::getBody).orElse(null);
        } catch(TMBCommonException tex){
            throw tex;
        } catch (Exception e) {
            logger.error("transfer-service on us confirm error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
    }
}
