package com.tmb.oneapp.mbtransferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.mbtransferservice.client.CustomerAccountBizClient;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.AccountSaving;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceRequest;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferResponse;
import com.tmb.oneapp.mbtransferservice.utils.TransferServiceUtils;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_ACTIVE;
import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_DORMANT;
import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_INACTIVE;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.ACCOUNT_STATUS_CLOSE;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_X_CORRELATION_ID;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_X_CRM_ID;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.ONE_APP_ACCOUNT_NUMBER_LENGTH;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.TOUCH_ACCOUNT_NUMBER_LENGTH;

@Service
@RequiredArgsConstructor
public class AccountTransferServiceImpl implements AccountTransferService {
    private static final TMBLogger<AccountTransferServiceImpl> logger = new TMBLogger<>(AccountTransferServiceImpl.class);
    public static final List<String> LIST_OF_DISPLAY_STATUS_DELETE_AND_CLOSE = Arrays.asList("02", "05");
    public static final Predicate<DepositAccount> DISPLAY_NOT_DELETE_OR_CLOSE_PREDICATE = depositAccount ->
            !LIST_OF_DISPLAY_STATUS_DELETE_AND_CLOSE.contains(depositAccount.getDisplayAccountStatus());

    private final CustomerAccountBizClient customerAccountBizClient;

    @Override
    public List<DepositAccountTransfer> getDepositAccountList(String correlationId, String crmId, Boolean refreshFlag, Boolean allAccountFlag) throws TMBCommonException {
        List<DepositAccount> response = new ArrayList<>();
        AccountSaving accountSaving = getAccountList(correlationId, crmId, refreshFlag, allAccountFlag);
        
        List<DepositAccount> eligibleAccountList = filterEligibleAccountList(accountSaving);
        List<DepositAccount> dormantOrHideAccountList = filterDormatOrHideAccountList(accountSaving);

        if (ObjectUtils.isEmpty(eligibleAccountList)) {
            throw TransferServiceUtils.failException(ResponseCode.NO_ELIGIBLE_ACCOUNT);
        }
        
        setAvailableBalanceForEligibleAccount(eligibleAccountList, correlationId, crmId);

        eligibleAccountList = filterOutCloseAccountAfterSyncStatus(eligibleAccountList);
        
        response.addAll(eligibleAccountList);
        response.addAll(dormantOrHideAccountList);
        
        if (ObjectUtils.isEmpty(eligibleAccountList)) {
            throw TransferServiceUtils.failException(ResponseCode.NO_ELIGIBLE_ACCOUNT);
        }

        return setAllowTransferOwnAndOtherTTB(response);
    }

    public static List<DepositAccount> filterOutCloseAccountAfterSyncStatus(List<DepositAccount> eligibleAccountList) {
        Predicate<DepositAccount> isNotStatusClose = d -> !StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_CLOSE, d.getAccountStatus());

        return eligibleAccountList.stream()
                .filter(isNotStatusClose)
                .toList();
    }

    public static List<DepositAccount> filterDormatOrHideAccountList(AccountSaving accountSaving) {
        Predicate<DepositAccount> isStatusDormant = e -> (e.getAccountStatus().equals(ACCOUNT_STATUS_DORMANT));

        Predicate<DepositAccount> isHide = DepositAccount::isHideAccountFlag;

        return accountSaving.getDepositAccountLists()
                .stream()
                .filter(isStatusDormant.or(isHide).and(DISPLAY_NOT_DELETE_OR_CLOSE_PREDICATE))
                .toList();
    }

    public static List<DepositAccount> filterEligibleAccountList(AccountSaving accountSaving) {
        Predicate<DepositAccount> isStatusActiveOrInactive = e -> (
                e.getAccountStatus().equals(ACCOUNT_STATUS_INACTIVE)
                        || e.getAccountStatus().equals(ACCOUNT_STATUS_ACTIVE));

        Predicate<DepositAccount> isNotHide = e -> (!e.isHideAccountFlag());
        
        return accountSaving.getDepositAccountLists()
                .stream()
                .filter(isStatusActiveOrInactive.and(isNotHide).and(DISPLAY_NOT_DELETE_OR_CLOSE_PREDICATE))
                .toList();
    }

    public AccountSaving getAccountList(String correlationId, String crmId, Boolean refreshFlag, Boolean allAccountFlag) throws TMBCommonException {
        try {
            return Optional.of(customerAccountBizClient.getAccountList(correlationId, crmId, refreshFlag, allAccountFlag))
                    .map(HttpEntity::getBody)
                    .map(TmbOneServiceResponse::getData)
                    .orElseThrow(() -> TransferServiceUtils.failException(ResponseCode.FAILED));
        } catch (FeignException.FeignClientException feignClientException) {
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
    }

    private void setAvailableBalanceForEligibleAccount(List<DepositAccount> depositAccountLists, String correlationId, String crmId) throws TMBCommonException {
        HttpHeaders requestBalanceHeaders = new HttpHeaders();
        requestBalanceHeaders.set(HEADER_X_CORRELATION_ID, correlationId);
        requestBalanceHeaders.set(HEADER_X_CRM_ID, crmId);

        for (DepositAccount d : depositAccountLists) {
            boolean isShouldNotGetBalance = d.getAvailableBalance() != null;
            if (isShouldNotGetBalance) {
                break;
            }

            AccountBalanceTransferResponse accountBalance = this.getAccountBalance(d.getAccountNumber(), requestBalanceHeaders);

            d.setAvailableBalance(accountBalance.getAvailableBalance());
            d.setLinkedAccount(accountBalance.getLinkedAccount());
            d.setAccountStatus(accountBalance.getAccountStatus());
            d.setAccountName(accountBalance.getAccountName());
            String accountStatusFromCBS = accountBalance.getAccountStatus();
            if (isActiveOrInactiveAccount(accountStatusFromCBS)) {
                break;
            }
        }
    }

    public static boolean isActiveOrInactiveAccount(String accountStatus) {
        return StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_ACTIVE, accountStatus) || StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_INACTIVE, accountStatus);
    }

    @Override
    public AccountBalanceTransferResponse getAccountBalance(final String accountNumber, HttpHeaders headers) throws TMBCommonException {
        validateRequest(accountNumber, headers);
        final String crmId = headers.getFirst(HEADER_X_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_X_CORRELATION_ID);
        final String accountType = TMBUtils.getAccountType(accountNumber);

        DepositAccount accountBalance = fetchAccountBalance(accountNumber, accountType, null, crmId, correlationId);

        return new AccountBalanceTransferResponse()
                .setAccountNumber(accountBalance.getAccountNumber())
                .setAvailableBalance(accountBalance.getAvailableBalance())
                .setAccountStatus(accountBalance.getAccountStatus())
                .setLinkedAccount(accountBalance.getLinkedAccount())
                .setAccountName(accountBalance.getAccountName());
    }

    public DepositAccount fetchAccountBalance(String accountNumber,
                                              String accountType,
                                              String financialId,
                                              String crmId,
                                              String correlationId) throws TMBCommonException {
        AccountBalanceRequest accountBalanceRequest = new AccountBalanceRequest()
                .setAccountNumber(accountNumber)
                .setAccountType(accountType)
                .setFinancialId(financialId);
        try {
            TmbOneServiceResponse<List<DepositAccount>> responseEntity = Objects.requireNonNull(customerAccountBizClient.getAccountBalance(crmId, correlationId, List.of(accountBalanceRequest)).getBody());

            boolean isCloseAccount = null == responseEntity.getData() || responseEntity.getData().isEmpty();
            if (isCloseAccount) {
                logger.info("Got empty or null from customerAccountBizClient.getAccountBalance is mean the account is closed or cannot found in CBS. [crmId = {}, request = {}]", crmId, accountBalanceRequest);
                return buildCloseAccount(accountNumber);
            }

            DepositAccount response = responseEntity.getData().get(0);
            logger.info("Get customerAccountBizClient.getAccountBalance : Success. [response.availableBalance = {}, response.accountStatus = {}, response.linkedAccount = {}]", response.getAvailableBalance(), response.getAccountStatus(), response.getLinkedAccount());
            validateMandatoryRequireNonNull(response);
            return response;

        } catch (FeignException e) {
            logger.error("Error process customerAccountBizClient.getAccountBalance. [correlationId = {}, request = {}]", correlationId, accountBalanceRequest, e);
            throw TransferServiceUtils.getTMBCommonException(ResponseCode.FAILED, "Error when fetch to get account balance");
        }
    }

    private void validateMandatoryRequireNonNull(DepositAccount response) {
        Objects.requireNonNull(response.getAvailableBalance(), "Mandatory field from CBS is null. Please contact to CBS. [availableBalance = null]");
        Objects.requireNonNull(response.getAccountStatus(), "Mandatory field from CBS is null. Please contact to CBS. [accountStatus = null]");
    }

    public static void validateRequest(String accountNumber, HttpHeaders headers) throws TMBCommonException {
        String crmId = headers.getFirst(HEADER_X_CRM_ID);
        String correlationId = headers.getFirst(HEADER_X_CORRELATION_ID);
        if (crmId == null || correlationId == null) {
            throw TransferServiceUtils.getTMBCommonException(ResponseCode.MISSING_REQUIRED_FIELD, HttpStatus.BAD_REQUEST);
        }

        if (accountNumber == null) {
            throw TransferServiceUtils.getTMBCommonException(ResponseCode.MISSING_REQUIRED_FIELD, HttpStatus.BAD_REQUEST);
        }

        int accLength = accountNumber.length();
        boolean validAccountLength = accLength == ONE_APP_ACCOUNT_NUMBER_LENGTH || accLength == TOUCH_ACCOUNT_NUMBER_LENGTH;
        if (!validAccountLength) {
            throw TransferServiceUtils.getTMBCommonException(ResponseCode.INVALID_ACCOUNT_NUMBER, HttpStatus.BAD_REQUEST);
        }
    }

    private List<DepositAccountTransfer> setAllowTransferOwnAndOtherTTB(List<DepositAccount> depositAccounts) {
        List<DepositAccountTransfer> depositAccountTransfers = new ArrayList<>();

        depositAccounts.forEach(depositAccount -> {
            DepositAccountTransfer depositAccountTransfer = new DepositAccountTransfer();
            BeanUtils.copyProperties(depositAccount, depositAccountTransfer);

            if (depositAccountTransfer.getTransferOwnTTBMapCode() != null) {
                String[] ownMapList = depositAccountTransfer.getTransferOwnTTBMapCode().split("");
                depositAccountTransfer.setAllowTransferOwnTtbDda(ownMapList[0]);
                depositAccountTransfer.setAllowTransferOwnTtbSda(ownMapList[1]);
                depositAccountTransfer.setAllowTransferOwnTtbCda(ownMapList[2]);
            }
            if (depositAccountTransfer.getTransferOtherTTBMapCode() != null) {
                String[] otherMapList = depositAccountTransfer.getTransferOtherTTBMapCode().split("");
                depositAccountTransfer.setAllowTransferOtherTtbDda(otherMapList[0]);
                depositAccountTransfer.setAllowTransferOtherTtbSda(otherMapList[1]);
                depositAccountTransfer.setAllowTransferOtherTtbCda(otherMapList[2]);
            }

            depositAccountTransfers.add(depositAccountTransfer);
        });
        return depositAccountTransfers;
    }

    private DepositAccount buildCloseAccount(String accountNumber) {
        return new DepositAccount()
                .setAccountNumber(accountNumber)
                .setAccountStatus(ACCOUNT_STATUS_CLOSE)
                .setAvailableBalance(new BigDecimal("0.00"))
                .setLinkedAccount(null);
    }

    public boolean isShouldGetBalance(String accountStatus, boolean isHideAccount, BigDecimal availableBalance) {
        boolean isNotDormant = !StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_DORMANT, accountStatus);
        boolean isNotHideAccount = !isHideAccount;
        boolean isAvailableBalanceNull = availableBalance == null;

        return isNotDormant && isNotHideAccount && isAvailableBalanceNull;
    }
}
