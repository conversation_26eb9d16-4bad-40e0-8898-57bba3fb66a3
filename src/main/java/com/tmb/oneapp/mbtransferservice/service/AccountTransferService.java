package com.tmb.oneapp.mbtransferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferResponse;
import org.springframework.http.HttpHeaders;

import java.util.List;

public interface AccountTransferService {
    List<DepositAccountTransfer> getDepositAccountList(String correlationId, String crmId, Boolean refreshFlag, Boolean allAccountFlag) throws TMBCommonException;

    AccountBalanceTransferResponse getAccountBalance(String accountNumber, HttpHeaders httpHeaders) throws TMBCommonException;
}
