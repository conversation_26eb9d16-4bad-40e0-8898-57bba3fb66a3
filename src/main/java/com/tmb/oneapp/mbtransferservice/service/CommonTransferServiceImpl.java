package com.tmb.oneapp.mbtransferservice.service;

import com.google.common.base.Strings;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.mbtransferservice.client.CustomerServiceClient;
import com.tmb.oneapp.mbtransferservice.client.PaymentExpServiceClient;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransferMapper;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.V2DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferResponse;
import com.tmb.oneapp.mbtransferservice.model.fx.FXExchangeRateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.service.v2.V2AccountTransferService;
import com.tmb.oneapp.mbtransferservice.utils.TransferServiceUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommonTransferServiceImpl implements CommonTransferService {

    private static final TMBLogger<CommonTransferServiceImpl> logger = new TMBLogger<>(CommonTransferServiceImpl.class);
    private final AccountTransferService accountTransferService;
    private final V2AccountTransferService v2AccountTransferService;
    private final CustomerServiceClient customerServiceClient;
    private final PaymentExpServiceClient paymentExpServiceClient;

    @Override
    public DepositAccount validateAccountTransfer(String crmId, String correlationId, String fromAccountNo) throws TMBCommonException {
        try {
            List<DepositAccountTransfer> depositAccountResponse = accountTransferService.getDepositAccountList(correlationId, crmId, false, false);
            List<DepositAccount> depositAccounts = DepositAccountTransferMapper.INSTANCE.toDepositAccountList(depositAccountResponse);

            List<DepositAccount> filterAccount = depositAccounts.stream()
                    .filter(dp -> !dp.isHideAccountFlag() && !dp.isViewOnlyFlag() && StringUtils.equals(dp.getAccountNumber(), fromAccountNo))
                    .toList();

            DepositAccount foundDepositAccount = filterAccount.stream().findFirst().orElse(null);
            if (foundDepositAccount == null) {
                throw TransferServiceUtils.failException(ResponseCode.NO_ELIGIBLE_ACCOUNT);
            }

            boolean availableBalanceNotExisted = ObjectUtils.isEmpty(foundDepositAccount.getAvailableBalance());
            if (availableBalanceNotExisted) {
                HttpHeaders requestBalanceHeaders = initialAccountBalanceRequestHeaders(correlationId, crmId);
                updateAvailableBalance(foundDepositAccount, requestBalanceHeaders);
            }
            return foundDepositAccount;
        } catch (TMBCommonException ex) {
            throw ex;
        } catch (Exception e) {
            logger.error("customer-exp-service getAccountTransfer error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
    }

    @Override
    public Boolean isOverAvailableBalance(BigDecimal availableAmount, String amount) throws TMBCommonException {
        try {
            BigDecimal amt = new BigDecimal(amount);
            return availableAmount.compareTo(amt) < 0;
        } catch (Exception e) {
            logger.error("check balance error : {}", e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
    }


    @Override
    public String getCrmIdFromDeviceId(String crmId,String deviceId) throws TMBCommonException{
        if(Strings.isNullOrEmpty(crmId)){
            CustomerProfileStatus profile = Optional.ofNullable(customerServiceClient.getCrmIdFromDeviceId(deviceId)).map(ResponseEntity::getBody).map(TmbOneServiceResponse::getData).orElseThrow(()-> TransferServiceUtils.failException(ResponseCode.FAILED));
            checkCustomerActive(profile);
            return profile.getCrmId();
        }
        return crmId;
    }

    private void checkCustomerActive(CustomerProfileStatus customerProfileStatus) throws TMBCommonException {
        if (customerProfileStatus.getMbUserStatusId().equals(CommonConstant.MB_CUSTOMER_STATUS_PIN_LOCK)) {
            throw TransferServiceUtils.failException(ResponseCode.PIN_ERROR_LOCKED_CAUSE);
        }

        if (!customerProfileStatus.getEbCustomerStatusId().equals(CommonConstant.EB_CUSTOMER_STATUS)
                || !customerProfileStatus.getMbUserStatusId().equals(CommonConstant.MB_CUSTOMER_STATUS)) {
            logger.error("Customer is not active - invalid data");
            throw new TMBCommonException(
                    ResponseCode.FAILED.getCode(),
                    ResponseCode.FAILED.getMessage(),
                    ResponseCode.FAILED.getService(),
                    HttpStatus.OK,
                    null);
        }
    }
    private HttpHeaders initialAccountBalanceRequestHeaders(String correlationId, String crmId) {
        HttpHeaders headers = new HttpHeaders();
        headers.set(HEADER_X_CORRELATION_ID, correlationId);
        headers.set(HEADER_X_CRM_ID, crmId);
        return headers;
    }

    private void updateAvailableBalance(DepositAccount account, HttpHeaders headers) throws TMBCommonException {
        AccountBalanceTransferResponse balanceResponse = accountTransferService.getAccountBalance(account.getAccountNumber(), headers);
        account.setAvailableBalance(balanceResponse.getAvailableBalance());
        account.setLinkedAccount(balanceResponse.getLinkedAccount());
        account.setAccountStatus(balanceResponse.getAccountStatus());
        account.setAccountName(balanceResponse.getAccountName());
    }

    private void updateAvailableBalanceByFinancialID(DepositAccount account, HttpHeaders headers)
            throws TMBCommonException {
        AccountBalanceTransferResponse balanceResponse = v2AccountTransferService.getAccountBalance
                (account.getAccountNumber(), account.getAccountType(), account.getFinancialId(), headers);
        account.setAvailableBalance(balanceResponse.getAvailableBalance());
        account.setLinkedAccount(balanceResponse.getLinkedAccount());
        account.setAccountStatus(balanceResponse.getAccountStatus());
        account.setAccountName(balanceResponse.getAccountName());
    }


    @Override
    public DepositAccount validateAccountTransfer(String crmId,
                                                  String correlationId,
                                                  MbTransferOnUsValidateRequest request)
            throws TMBCommonException {
        try {
            V2DepositAccountTransfer v2DepositAccountTransfer = v2AccountTransferService
                    .getDepositAccountList(correlationId, crmId, false, false, null);

            List<DepositAccountTransfer> depositAccountResponse =
                    new ArrayList<>(v2DepositAccountTransfer.getAccountList());
            if(ObjectUtils.isNotEmpty(v2DepositAccountTransfer.getFcdAccountList())) {
                depositAccountResponse.addAll(v2DepositAccountTransfer.getFcdAccountList());
            }

            List<DepositAccount> depositAccounts =
            DepositAccountTransferMapper.INSTANCE.toDepositAccountList(depositAccountResponse);

            List<DepositAccount> filterAccount = depositAccounts.stream()
                    .filter(dp -> !dp.isHideAccountFlag()
                            && !dp.isViewOnlyFlag()
                            && StringUtils.equals(dp.getAccountNumber(), request.getFromAccountNo()))
                    .toList();

            DepositAccount foundDepositAccount = filterAccount.stream().findFirst().orElse(null);
            if (foundDepositAccount == null) {
                logger.info("Not found from-account in eligible account list {}", request.getFromAccountNo());
                throw TransferServiceUtils.failException(ResponseCode.NO_ELIGIBLE_ACCOUNT);
            }

            boolean availableBalanceNotExisted = ObjectUtils.isEmpty(foundDepositAccount.getAvailableBalance());
            if (availableBalanceNotExisted) {
                HttpHeaders requestBalanceHeaders = initialAccountBalanceRequestHeaders(correlationId, crmId);
                if (TransferServiceUtils.isFcdAccount(foundDepositAccount.getAccountNumber())) {
                    updateAvailableBalanceByFinancialID(foundDepositAccount, requestBalanceHeaders);
                } else {
                    updateAvailableBalance(foundDepositAccount, requestBalanceHeaders);
                }
            }

            validateFcdTransfer(crmId, correlationId, request, depositAccounts, foundDepositAccount);

            return foundDepositAccount;
        } catch (TMBCommonException ex) {
            throw ex;
        } catch (Exception e) {
            logger.error("customer-exp-service getAccountTransfer error : {}", e.getMessage(), e);
            throw TransferServiceUtils.failException(ResponseCode.FAILED);
        }
    }

    private void validateFcdTransfer(String crmId,
                                     String correlationId,
                                     MbTransferOnUsValidateRequest request,
                                     List<DepositAccount> depositAccounts,
                                     DepositAccount fromDepositAccount)
            throws TMBCommonException {
        if (TransferServiceUtils.isFcdAccount(fromDepositAccount.getAccountNumber())) {
            if (StringUtils.isBlank(request.getFxTransId())) {
                callGetFxRate(crmId, correlationId, request);
            }
            Optional<DepositAccount> toAccount = depositAccounts.stream()
                    .filter(dp -> StringUtils.equals(dp.getAccountNumber(), request.getToAccountNo()))
                    .toList().stream().findFirst();
            if(toAccount.isPresent()) {
                DepositAccount toDepositAccount = toAccount.get();
                validateCurrency(fromDepositAccount, toDepositAccount);
                validateTdToAccountType(fromDepositAccount, toDepositAccount);
            } else {
                validateTdToOwnAccount(fromDepositAccount);
            }
        }
    }

    private static void validateTdToOwnAccount(DepositAccount fromDepositAccount) throws TMBCommonException {
        if(ACCOUNT_TYPE_CDA.equals(fromDepositAccount.getAccountType())) {
            logger.error("The to-account is not an owner FCD account");
            throw TransferServiceUtils.failException(ResponseCode.NO_ELIGIBLE_ACCOUNT);
        }
    }

    private static void validateTdToAccountType(DepositAccount fromDepositAccount, DepositAccount toDepositAccount) throws TMBCommonException {
        if(ACCOUNT_TYPE_CDA.equals(fromDepositAccount.getAccountType())
                && ACCOUNT_TYPE_CDA.equals(toDepositAccount.getAccountType())) {
            logger.error("The to-account is an another FCD TD account");
            throw TransferServiceUtils.failException(ResponseCode.NO_ELIGIBLE_ACCOUNT);
        }
    }

    private static void validateCurrency(DepositAccount fromDepositAccount, DepositAccount toDepositAccount) throws TMBCommonException {
        if (!StringUtils.equals(toDepositAccount.getAcctCtl2(), fromDepositAccount.getAcctCtl2())) {
            logger.error("Currency is not matching, from-acctCtl2: {}, to-acctCtl2: {}",
                    toDepositAccount.getAcctCtl2(), fromDepositAccount.getAcctCtl2());
            throw TransferServiceUtils.failException(ResponseCode.NO_ELIGIBLE_ACCOUNT);
        }
    }

    private void callGetFxRate(String crmId, String correlationId, MbTransferOnUsValidateRequest request) {
        ResponseEntity<TmbServiceResponse<FXExchangeRateResponse>> exchangeRates =
                paymentExpServiceClient.getExchangeRates(correlationId, crmId);
        Optional.ofNullable(exchangeRates.getBody())
                .map(TmbServiceResponse::getData)
                .ifPresent(data -> request.setFxTransId(data.getTransId()));
    }

    @Override
    public Boolean isNonWorkingHour(String nonServiceHourStart, String nonServiceHourEnd, Date currentDate) {
        String nowTime = new SimpleDateFormat(SIMPLE_TIME_FORMAT).format(currentDate);

        if (nonServiceHourEnd.compareTo(nonServiceHourStart) < 0) {
            return (nonServiceHourStart.compareTo(nowTime) <= 0) ||
                    (nonServiceHourEnd.compareTo(nowTime) >= 0);
        } else {
            return (nonServiceHourStart.compareTo(nowTime) <= 0) &&
                    (nonServiceHourEnd.compareTo(nowTime) >= 0);
        }
    }

}
