package com.tmb.oneapp.mbtransferservice.service.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.mbtransferservice.client.TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.AccountSaving;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.TransferModuleModel;
import com.tmb.oneapp.mbtransferservice.model.V2DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferResponse;
import com.tmb.oneapp.mbtransferservice.service.AccountTransferServiceImpl;
import com.tmb.oneapp.mbtransferservice.utils.TransferServiceUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_ACTIVE;
import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_DORMANT;
import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_INACTIVE;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.ACCOUNT_STATUS_CLOSE;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.ACCOUNT_TYPE_CDA;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_X_CORRELATION_ID;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_X_CRM_ID;
import static com.tmb.oneapp.mbtransferservice.service.AccountTransferServiceImpl.DISPLAY_NOT_DELETE_OR_CLOSE_PREDICATE;
import static com.tmb.oneapp.mbtransferservice.utils.TransferServiceUtils.getVersion;

@Primary
@Service
@RequiredArgsConstructor
public class V2AccountTransferServiceImpl implements V2AccountTransferService {

    private static final TMBLogger<V2AccountTransferServiceImpl> logger =
            new TMBLogger<>(V2AccountTransferServiceImpl.class);

    private final AccountTransferServiceImpl accountTransferService;
    private final TransferServiceClient transferServiceClient;

    @Value("${fcd-td.allow-transfer.app-version:}")
    private String fcdTdAppVersion;

    @Override
    public V2DepositAccountTransfer getDepositAccountList(String correlationId,
                                                          String crmId,
                                                          Boolean refreshFlag,
                                                          Boolean allAccountFlag,
                                                          String appVersion) throws TMBCommonException {
        logger.info("[V2] get deposit account list begin");
        V2DepositAccountTransfer accounts = new V2DepositAccountTransfer();
        AccountSaving accountSaving = accountTransferService
                .getAccountList(correlationId, crmId, refreshFlag, allAccountFlag);
        getAccountTransfer(correlationId, crmId, accounts, accountSaving, appVersion);
        return accounts;
    }

    @Override
    public AccountBalanceTransferResponse getAccountBalance(String accountNumber,
                                                            String accountType,
                                                            String financialId,
                                                            HttpHeaders httpHeaders) throws TMBCommonException {
        AccountTransferServiceImpl.validateRequest(accountNumber, httpHeaders);
        final String crmId = httpHeaders.getFirst(HEADER_X_CRM_ID);
        final String correlationId = httpHeaders.getFirst(HEADER_X_CORRELATION_ID);
        if(StringUtils.isBlank(accountType)) {
            accountType = TMBUtils.getAccountType(accountNumber);
        }

        DepositAccount accountBalance = accountTransferService
                .fetchAccountBalance(accountNumber, accountType, financialId, crmId, correlationId);

        return new AccountBalanceTransferResponse()
                .setAccountNumber(accountBalance.getAccountNumber())
                .setAvailableBalance(accountBalance.getAvailableBalance())
                .setAccountStatus(accountBalance.getAccountStatus())
                .setLinkedAccount(accountBalance.getLinkedAccount())
                .setAccountName(accountBalance.getAccountName());
    }

    private void getAccountTransfer(String correlationId,
                                    String crmId,
                                    V2DepositAccountTransfer accounts,
                                    AccountSaving accountSaving,
                                    String appVersion)
            throws TMBCommonException {

        List<DepositAccount> accountList =
                new ArrayList<>(Optional.ofNullable(accountSaving.getDepositAccountLists())
                .orElse(Collections.emptyList()));
        List<DepositAccount> fcdAccountList =
                new ArrayList<>(Optional.ofNullable(accountSaving.getFcdAccountLists())
                .orElse(Collections.emptyList()));

        Predicate<DepositAccount> isStatusActiveOrInactive =
                depositAccount -> (ACCOUNT_STATUS_INACTIVE.equals(depositAccount.getAccountStatus())
                        || ACCOUNT_STATUS_ACTIVE.equals(depositAccount.getAccountStatus()));
        Predicate<DepositAccount> isStatusDormant =
                depositAccount -> ACCOUNT_STATUS_DORMANT.equals(depositAccount.getAccountStatus());
        Predicate<DepositAccount> isHide = DepositAccount::isHideAccountFlag;

        ArrayList<DepositAccount> eligibleAccountList = new ArrayList<>(accountList.stream()
                .filter(isStatusActiveOrInactive.and(isHide.negate())
                        .and(DISPLAY_NOT_DELETE_OR_CLOSE_PREDICATE))
                .toList());
        accountList.removeAll(eligibleAccountList);
        List<DepositAccount> dormantOrHideAccountList = new ArrayList<>(accountList.stream()
                .filter(isStatusDormant.or(isHide)
                        .and(DISPLAY_NOT_DELETE_OR_CLOSE_PREDICATE))
                .toList());

        ArrayList<DepositAccount> eligibleFcdAccountList = new ArrayList<>(fcdAccountList.stream()
                .filter(isStatusActiveOrInactive.and(isHide.negate())
                        .and(DISPLAY_NOT_DELETE_OR_CLOSE_PREDICATE))
                .toList());
        fcdAccountList.removeAll(eligibleFcdAccountList);
        List<DepositAccount> dormantOrHideFcdAccountList = new ArrayList<>(fcdAccountList.stream()
                .filter(isStatusDormant.or(isHide)
                        .and(DISPLAY_NOT_DELETE_OR_CLOSE_PREDICATE))
                .toList());

        if (ObjectUtils.isEmpty(eligibleAccountList) && ObjectUtils.isEmpty(eligibleFcdAccountList)) {
            throw TransferServiceUtils.failException(ResponseCode.NO_ELIGIBLE_ACCOUNT);
        }

        boolean hashEligibleAccount =
                setAvailableBalanceForEligibleAccount(eligibleAccountList, correlationId, crmId, false);
        if(!hashEligibleAccount) {
            setAvailableBalanceForEligibleAccount(eligibleFcdAccountList, correlationId, crmId, true);
        }

        if (ObjectUtils.isEmpty(eligibleAccountList) && ObjectUtils.isEmpty(eligibleAccountList)) {
            throw TransferServiceUtils.failException(ResponseCode.NO_ELIGIBLE_ACCOUNT);
        }

        List<DepositAccount> finalAccountList = new ArrayList<>();
        finalAccountList.addAll(eligibleAccountList);
        finalAccountList.addAll(dormantOrHideAccountList);

        List<DepositAccount> finalFcdAccountList = new ArrayList<>();
        finalFcdAccountList.addAll(eligibleFcdAccountList);
        finalFcdAccountList.addAll(dormantOrHideFcdAccountList);

        setTransferMinimumAmount(correlationId, finalFcdAccountList);

        accounts.setAccountList(setAllowTransferOwnAndOtherTTB(finalAccountList, false, appVersion));
        accounts.setFcdAccountList(setAllowTransferOwnAndOtherTTB(finalFcdAccountList, true, appVersion));

    }

    private void setTransferMinimumAmount(String correlationId, List<DepositAccount> finalFcdAccountList) {
        String transferMinimumAmount = getTransferMinimumAmount(correlationId);
        if(Objects.nonNull(transferMinimumAmount)) {
            finalFcdAccountList.forEach(depositAccount -> {
                if ("CDA".equalsIgnoreCase(depositAccount.getAccountType())) {
                    depositAccount.setTransferMinimumAmount(transferMinimumAmount);
                }
            });
        }
    }

    private String getTransferMinimumAmount(String correlationId) {
        try {
            ResponseEntity<TmbOneServiceResponse<TransferModuleModel>> responseEntity =
                    transferServiceClient.getAccountConfiguration(correlationId);
            return Optional.of(responseEntity).map(ResponseEntity::getBody)
                    .map(TmbOneServiceResponse::getData)
                    .map(TransferModuleModel::getFcdTdMinimumAmount)
                    .map(BigDecimal::toString)
                    .orElse(null);
        } catch (Exception e){
            return null;
        }
    }

    private boolean setAvailableBalanceForEligibleAccount(ArrayList<DepositAccount> depositAccountLists,
                                                          String correlationId,
                                                          String crmId,
                                                          boolean isFcd)
            throws TMBCommonException {
        boolean hashEligibleAccount = false;
        HttpHeaders requestBalanceHeaders = new HttpHeaders();
        requestBalanceHeaders.set(HEADER_X_CORRELATION_ID, correlationId);
        requestBalanceHeaders.set(HEADER_X_CRM_ID, crmId);
        List<DepositAccount> closedAccoutlist = new ArrayList<>();
        for (DepositAccount depositAccount : depositAccountLists) {
            boolean isShouldNotGetBalance = depositAccount.getAvailableBalance() != null;
            if (isShouldNotGetBalance) {
                hashEligibleAccount = true;
                break;
            }

            AccountBalanceTransferResponse accountBalance = getAccountBalance
                    (
                            depositAccount.getAccountNumber(),
                            depositAccount.getAccountType(),
                            (isFcd ? depositAccount.getFinancialId() : null),
                            requestBalanceHeaders
                    );

            depositAccount.setAvailableBalance(accountBalance.getAvailableBalance());
            depositAccount.setLinkedAccount(accountBalance.getLinkedAccount());
            depositAccount.setAccountStatus(accountBalance.getAccountStatus());
            depositAccount.setAccountName(accountBalance.getAccountName());
            String accountStatusFromCBS = accountBalance.getAccountStatus();
            if (AccountTransferServiceImpl.isActiveOrInactiveAccount(accountStatusFromCBS)) {
                hashEligibleAccount = true;
                break;
            } else {
                if(StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_CLOSE, depositAccount.getAccountStatus())) {
                    closedAccoutlist.add(depositAccount);
                }
            }
        }
        depositAccountLists.removeAll(closedAccoutlist);
        return hashEligibleAccount;
    }

    private List<DepositAccountTransfer> setAllowTransferOwnAndOtherTTB(List<DepositAccount> depositAccounts,
                                                                        boolean isFcd,
                                                                        String appVersion) {
        List<DepositAccountTransfer> depositAccountTransfers = new ArrayList<>();

        depositAccounts.forEach(depositAccount -> {
            DepositAccountTransfer accountTransfer = new DepositAccountTransfer();
            BeanUtils.copyProperties(depositAccount, accountTransfer);

            if (accountTransfer.getTransferOwnTTBMapCode() != null) {
                String[] ownMapList = accountTransfer.getTransferOwnTTBMapCode().split("");
                accountTransfer.setAllowTransferOwnTtbDda(ownMapList[0]);
                accountTransfer.setAllowTransferOwnTtbSda(ownMapList[1]);
                accountTransfer.setAllowTransferOwnTtbCda(ownMapList[2]);
            }
            if (accountTransfer.getTransferOtherTTBMapCode() != null) {
                String[] otherMapList = accountTransfer.getTransferOtherTTBMapCode().split("");
                accountTransfer.setAllowTransferOtherTtbDda(otherMapList[0]);
                accountTransfer.setAllowTransferOtherTtbSda(otherMapList[1]);
                accountTransfer.setAllowTransferOtherTtbCda(otherMapList[2]);
            }
            accountTransfer.setIsFcd(isFcd);
            if(isFcd && shouldNotAllowFcdTdTransfer(accountTransfer.getAccountType(), appVersion, fcdTdAppVersion)) {
                accountTransfer.setAllowTransferFromAccount("0");
            }
            depositAccountTransfers.add(accountTransfer);
        });
        return depositAccountTransfers;
    }

    public static boolean shouldNotAllowFcdTdTransfer(String accountType,
                                                      String userAppVersion,
                                                      String targetAppVersion) {
        try {
            return ACCOUNT_TYPE_CDA.equals(accountType)
                    && getVersion(userAppVersion).compareTo(getVersion(targetAppVersion)) < 0;
        } catch (Exception ex) {
            logger.error("Failed to get user app version [{}].", userAppVersion, ex);
            return true;
        }
    }

}
