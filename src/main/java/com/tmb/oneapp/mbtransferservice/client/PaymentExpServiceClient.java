package com.tmb.oneapp.mbtransferservice.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.model.fx.FXExchangeRateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${feign.payment.exp.service.name}", url = "${feign.payment.exp.service.url}")
public interface PaymentExpServiceClient {

    @GetMapping(value = "/apis/payment/fx/exchange-rate", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<FXExchangeRateResponse>> getExchangeRates(
            @RequestHeader(value = CommonConstant.HEADER_X_CORRELATION_ID) String correlationID,
            @RequestHeader(value = CommonConstant.HEADER_X_CRM_ID) String crmId);

}