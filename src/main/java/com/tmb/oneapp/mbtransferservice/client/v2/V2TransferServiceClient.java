package com.tmb.oneapp.mbtransferservice.client.v2;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayValidateResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_DEVICE_ID;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_DEVICE_MODEL;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_IP_ADDRESS;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_TIMESTAMP;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_X_CORRELATION_ID;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_X_CRM_ID;

@FeignClient(name = "${feign.transfer.service.name}", url = "${feign.transfer.service.url}")
public interface V2TransferServiceClient {
    @PostMapping(value = "/v2/transfer-service/off-us/validate", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<TransferOffUsValidateResponse>> offUsValidate(
            @RequestHeader(value = HEADER_X_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_X_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_IP_ADDRESS) String ipAddress,
            @RequestHeader(value = HEADER_APP_VERSION) String appVersion,
            @RequestBody TransferOffUsValidateRequest request);

    @PostMapping(value = "/v2/transfer-service/off-us/confirm", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<TransferOffUsConfirmResponse>> offUsConfirm(
            @RequestHeader(value = HEADER_X_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_X_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_DEVICE_ID) String deviceId,
            @RequestHeader(value = HEADER_APP_VERSION) String appVersion,
            @RequestHeader(value = HEADER_DEVICE_MODEL) String deviceModel,
            @RequestHeader(value = HEADER_TIMESTAMP) String timestamp,
            @RequestHeader(value = HEADER_IP_ADDRESS) String ipAddress,
            @RequestBody TransferOffUsConfirmRequest request);

    @PostMapping(value = "/v2/transfer-service/promptpay/validate", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<TransferPromptpayValidateResponse>> promptpayValidate(
            @RequestHeader(value = HEADER_X_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_X_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_DEVICE_ID) String deviceId,
            @RequestHeader(value = CommonConstant.HEADER_PRE_LOGIN) String preLogin,
            @RequestHeader(value = HEADER_IP_ADDRESS) String ipAddress,
            @RequestHeader(value = HEADER_APP_VERSION) String appVersion,
            @RequestBody TransferPromptpayValidateRequest request);

    @PostMapping(value = "/v2/transfer-service/on-us/validate", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<TransferOnUsValidateResponse>> onUsValidate(
            @RequestHeader(value = HEADER_X_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_X_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_DEVICE_ID) String deviceId,
            @RequestHeader(value = HEADER_IP_ADDRESS) String ipAddress,
            @RequestHeader(value = HEADER_APP_VERSION) String appVersion,
            @RequestBody TransferOnUsValidateRequest request);

    @PostMapping(value = "/v2/transfer-service/on-us/confirm", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<TransferOnUsConfirmResponse>> onUsConfirm(
            @RequestHeader(value = HEADER_X_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_X_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_DEVICE_ID) String deviceId,
            @RequestHeader(value = HEADER_IP_ADDRESS) String ipAddress,
            @RequestHeader(value = HEADER_APP_VERSION) String appVersion,
            @RequestBody MbTransferOnUsConfirmRequest request);


    @PostMapping(value = "/v2/transfer-service/promptpay/confirm", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<TransferPromptpayConfirmResponse>> promptpayConfirm(
            @RequestHeader(value = HEADER_X_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_X_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_DEVICE_ID) String deviceId,
            @RequestHeader(value = HEADER_APP_VERSION) String appVersion,
            @RequestHeader(value = HEADER_DEVICE_MODEL) String deviceModel,
            @RequestHeader(value = HEADER_TIMESTAMP) String timestamp,
            @RequestHeader(value = HEADER_IP_ADDRESS) String ipAddress,
            @RequestBody TransferPromptpayConfirmRequest request);
}
