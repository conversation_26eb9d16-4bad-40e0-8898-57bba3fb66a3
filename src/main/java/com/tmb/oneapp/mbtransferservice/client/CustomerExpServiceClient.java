package com.tmb.oneapp.mbtransferservice.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(name = "${feign.customers.exp.service.name}", url = "${feign.customers.exp.service.url}")
public interface CustomerExpServiceClient {

    @GetMapping(value = "/apis/customer/accounts/transfer", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<List<DepositAccount>>> getAccountsTransfer(
            @RequestHeader(value = CommonConstant.HEADER_X_CORRELATION_ID) String correlationID,
            @RequestHeader(value = CommonConstant.HEADER_X_CRM_ID) String crmId);
}
