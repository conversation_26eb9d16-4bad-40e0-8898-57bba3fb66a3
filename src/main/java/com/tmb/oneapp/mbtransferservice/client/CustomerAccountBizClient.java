package com.tmb.oneapp.mbtransferservice.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.model.AccountSaving;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

@FeignClient(name = "${feign.customers.account.biz.service.name}", url = "${feign.customers.account.biz.service.url}")
public interface CustomerAccountBizClient {

    @GetMapping(value = "/v1/customer-account-biz/accounts-list", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<AccountSaving>> getAccountList(
            @RequestHeader(value = CommonConstant.HEADER_X_CORRELATION_ID) String correlationID,
            @RequestHeader(value = CommonConstant.HEADER_X_CRM_ID) String crmId,
            @RequestHeader(value = CommonConstant.REFRESH_FLAG) Boolean refreshFlag,
            @RequestHeader(value = CommonConstant.ALL_ACCOUNT_FLAG) Boolean allAccountFlag);

    @PostMapping(value = "/v1/customer-account-biz/account-balance", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<List<DepositAccount>>> getAccountBalance(
            @RequestHeader(value = CommonConstant.HEADER_X_CRM_ID) String crmId,
            @RequestHeader(value = CommonConstant.HEADER_X_CORRELATION_ID) String correlationID,
            @RequestBody List<AccountBalanceRequest> accountRequest);
}
