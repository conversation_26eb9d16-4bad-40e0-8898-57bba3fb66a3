package com.tmb.oneapp.mbtransferservice.constant;


import lombok.Getter;

import java.io.Serializable;

@Getter
public enum ResponseCode implements Serializable {

    SUCCESS("0000", "success", Constants.SERVICE_NAME),
    FAILED("0001", "failed", Constants.SERVICE_NAME),
    NO_ELIGIBLE_ACCOUNT("no_eligible_error", "This account no eligible account", Constants.SERVICE_NAME),
    PIN_ERROR_LOCKED_CAUSE("pin_error_locked_cause", "Your PIN is locked because you have reached the maximum attempts.", Constants.SERVICE_NAME),
    MISSING_REQUIRED_FIELD("1000014", "Missing required field", Constants.SERVICE_NAME),
    INVALID_ACCOUNT_NUMBER("1000015", "Invalid Account Number", Constants.SERVICE_NAME),
    INVALID_ACCOUNT_TYPE("1000016", "Invalid Account Type", Constants.SERVICE_NAME),
    INVALID_REQUEST("0010", "Invalid Request", Constants.SERVICE_NAME),
    INVALID_ACCOUNT_STATUS("1142-B-01-IC-03-01-007", "Invalid account status", Constants.SERVICE_NAME),
    INSUFFICIENT_FUNDS("1151-B-01-IC-01-01-000", "Insufficient funds", Constants.SERVICE_NAME),
    UNAVAILABLE_SERVICE_HOUR("1210-B-01-IC-01-01-000", "Non Service Hours", Constants.SERVICE_NAME),
    CONNECTION_FAIL("2300-B-01-IC-01-01-000", "Connection Fail", Constants.SERVICE_NAME);

    private final String code;
    private final String message;
    private final String service;
    private final String desc;

    private static class Constants {
        public static final String SERVICE_NAME = "mb-transfer-service";
    }

    ResponseCode(String code, String message, String service) {
        this.code = code;
        this.message = message;
        this.service = service;
        this.desc = null;
    }
}