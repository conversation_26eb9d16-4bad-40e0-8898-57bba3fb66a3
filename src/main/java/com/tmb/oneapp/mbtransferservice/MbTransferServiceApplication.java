package com.tmb.oneapp.mbtransferservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = { "com.ttb", "com.tmb.oneapp", "com.tmb.common"})
@EnableFeignClients
public class MbTransferServiceApplication {

	public static void main(String[] args) {

		SpringApplication.run(MbTransferServiceApplication.class, args);
	}

}
