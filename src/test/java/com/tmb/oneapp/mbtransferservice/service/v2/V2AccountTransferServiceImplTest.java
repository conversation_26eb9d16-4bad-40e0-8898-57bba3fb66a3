package com.tmb.oneapp.mbtransferservice.service.v2;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.assertEquals;

class V2AccountTransferServiceImplTest {

    @ParameterizedTest
    @CsvSource(value = {"CDA,5.6.2,5.6.2,false",
            "CDA,,5.6.2,true",
            "CDA,5.6.2,,true",
            "CDA,null,5.6.2,true",
            "CDA,5.6.2,null,true",
            "CDA,,,true",
            "CDA,null,null,true",
            "CDA,5.6.2,5.6.2,false",
            "CDA,5.6.3,5.6.2,false",
            "CDA,5.6.1,5.6.2,true",
            "SDA,,,false"},
            nullValues = "null")
    void shouldNotAllowFcdTdTransfer(String accountType,
                                     String appVersion,
                                     String tdAppVersion,
                                     Boolean expect) {
        assertEquals(expect, V2AccountTransferServiceImpl
                .shouldNotAllowFcdTdTransfer(accountType, appVersion, tdAppVersion));
    }

}