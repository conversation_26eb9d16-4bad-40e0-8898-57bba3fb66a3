package com.tmb.oneapp.mbtransferservice.service.v3;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.mbtransferservice.client.TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.client.v2.V2TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.CommonTime;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.ServiceHourResponse;
import com.tmb.oneapp.mbtransferservice.model.TransferModuleModel;
import com.tmb.oneapp.mbtransferservice.model.offus.MbTransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.service.CommonTransferService;
import feign.FeignException;
import jakarta.validation.constraints.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class V3TransferServiceImplTest {

    @InjectMocks
    V3TransferServiceImpl v3TransferService;

    @Mock
    V2TransferServiceClient transferServiceClient;
    @Mock
    TransferServiceClient transferClient;
    @Mock
    CommonTransferService commonTransferService;

    private String correlationId;
    private String crmId;
    private String contentSignaturePinfree;
    private HttpHeaders httpHeaders;

    @BeforeEach
    void setUp() {
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        crmId = "001100000000000000000001184383";
        contentSignaturePinfree = "test-signature-pinfree";
        httpHeaders = new HttpHeaders();
        httpHeaders.add("X-Forward-For", "127.0.0.1");
        httpHeaders.add("App-Version", "1.0.0");
        httpHeaders.add("device-id", "test-device-id");
        httpHeaders.add("Device-Model", "test-device-model");
    }

    @Test
    void validateTdCutoffTime() throws TMBCommonException {
        CommonTime commonTime = new CommonTime();
        commonTime.setStart(plusHours(1));
        commonTime.setEnd(plusHours(2));
        CommonTime commonTime2 = new CommonTime();
        commonTime2.setStart(minusHours(1));
        commonTime2.setEnd(plusHours(1));

        TmbOneServiceResponse<TransferModuleModel> response = new TmbOneServiceResponse<>();
        response.setData(TransferModuleModel.builder().withdrawTdCutoffTime(commonTime).build());
        TmbOneServiceResponse<TransferModuleModel> response2 = new TmbOneServiceResponse<>();
        response2.setData(TransferModuleModel.builder().withdrawTdCutoffTime(commonTime2).build());
        
        when(transferClient.getAccountConfiguration(anyString()))
                .thenReturn(ResponseEntity.ok(response))
                .thenReturn(ResponseEntity.ok(response2))
                .thenThrow(FeignException.class);
        when(commonTransferService.isNonWorkingHour(anyString(),anyString(),any()))
                .thenReturn(true)
                .thenReturn(false);
        
        ServiceHourResponse serviceHourResponse =
                v3TransferService.validateTdCutoffTime(correlationId, contentSignaturePinfree);
        assertTrue(serviceHourResponse.isNonServiceHour());
        
        serviceHourResponse =
                v3TransferService.validateTdCutoffTime(correlationId, contentSignaturePinfree);
        assertFalse(serviceHourResponse.isNonServiceHour());
        
        assertThrows(TMBCommonException.class, () ->
                v3TransferService.validateTdCutoffTime(correlationId, contentSignaturePinfree));
    }

    @Test
    void testOffUsValidate_Success() throws TMBCommonException {
        // Arrange
        MbTransferOffUsValidateRequest request = createMockOffUsValidateRequest();
        DepositAccount depositAccount = createMockDepositAccount();
        TmbServiceResponse<TransferOffUsValidateResponse> expectedResponse = createMockOffUsValidateResponse();

        when(commonTransferService.validateAccountTransfer(anyString(), anyString(), anyString()))
                .thenReturn(depositAccount);
        when(commonTransferService.isOverAvailableBalance(any(BigDecimal.class), anyString()))
                .thenReturn(false);
        when(transferServiceClient.offUsValidate(anyString(), anyString(), anyString(), anyString(), any(TransferOffUsValidateRequest.class)))
                .thenReturn(ResponseEntity.ok(expectedResponse));

        // Act
        TmbServiceResponse<TransferOffUsValidateResponse> result = 
                v3TransferService.offUsValidate(correlationId, crmId, contentSignaturePinfree, httpHeaders, request);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResponse.getData().getTransId(), result.getData().getTransId());
    }

    @Test
    void testOffUsConfirm_Success() throws TMBCommonException {
        // Arrange
        TransferOffUsConfirmRequest request = createMockOffUsConfirmRequest();
        TmbServiceResponse<TransferOffUsConfirmResponse> expectedResponse = createMockOffUsConfirmResponse();

        when(transferServiceClient.offUsConfirm(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), any(TransferOffUsConfirmRequest.class)))
                .thenReturn(ResponseEntity.ok(expectedResponse));

        // Act
        TmbServiceResponse<TransferOffUsConfirmResponse> result = 
                v3TransferService.offUsConfirm(correlationId, crmId, "deviceId", "timestamp", contentSignaturePinfree, httpHeaders, request);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResponse.getData().getReferenceNo(), result.getData().getReferenceNo());
    }

    @Test
    void testOnUsValidate_Success() throws TMBCommonException {
        // Arrange
        MbTransferOnUsValidateRequest request = createMockOnUsValidateRequest();
        DepositAccount depositAccount = createMockDepositAccount();
        TmbServiceResponse<TransferOnUsValidateResponse> expectedResponse = createMockOnUsValidateResponse();

        when(commonTransferService.validateAccountTransfer(anyString(), anyString(), any(MbTransferOnUsValidateRequest.class)))
                .thenReturn(depositAccount);
        when(commonTransferService.isOverAvailableBalance(any(BigDecimal.class), anyString()))
                .thenReturn(false);
        when(transferServiceClient.onUsValidate(anyString(), anyString(), anyString(), anyString(), anyString(), any(TransferOnUsValidateRequest.class)))
                .thenReturn(ResponseEntity.ok(expectedResponse));

        // Act
        TmbServiceResponse<TransferOnUsValidateResponse> result = 
                v3TransferService.onUsValidate(correlationId, crmId, contentSignaturePinfree, httpHeaders, request);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResponse.getData().getTransId(), result.getData().getTransId());
    }

    @Test
    void testOnUsConfirm_Success() throws TMBCommonException {
        // Arrange
        MbTransferOnUsConfirmRequest request = createMockOnUsConfirmRequest();
        TmbServiceResponse<TransferOnUsConfirmResponse> expectedResponse = createMockOnUsConfirmResponse();

        when(transferServiceClient.onUsConfirm(anyString(), anyString(), anyString(), anyString(), anyString(), any(MbTransferOnUsConfirmRequest.class)))
                .thenReturn(ResponseEntity.ok(expectedResponse));

        // Act
        TmbServiceResponse<TransferOnUsConfirmResponse> result = 
                v3TransferService.onUsConfirm(correlationId, crmId, contentSignaturePinfree, httpHeaders, request);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResponse.getData().getReferenceNo(), result.getData().getReferenceNo());
    }

    // Helper methods for creating mock objects
    private MbTransferOffUsValidateRequest createMockOffUsValidateRequest() {
        MbTransferOffUsValidateRequest request = new MbTransferOffUsValidateRequest();
        request.setFromAccountNo("**********");
        request.setToAccountNo("**********");
        request.setAmount("1000.00");
        request.setToBankCode("001");
        request.setToFavoriteName("Test Bank");
        request.setFlow("Setting");
        request.setNote("Test transfer");
        request.setCategoryId("1");
        request.setDepositNo("DEP001");
        return request;
    }

    private TransferOffUsConfirmRequest createMockOffUsConfirmRequest() {
        TransferOffUsConfirmRequest request = new TransferOffUsConfirmRequest();
        request.setTransId("TXN001");
        return request;
    }

    private MbTransferOnUsValidateRequest createMockOnUsValidateRequest() {
        MbTransferOnUsValidateRequest request = new MbTransferOnUsValidateRequest();
        request.setFromAccountNo("**********");
        request.setToAccountNo("**********");
        request.setAmount("1000.00");
        request.setToFavoriteName("Test Account");
        request.setFlow("Setting");
        request.setNote("Test transfer");
        request.setCategoryId("1");
        request.setDepositNo("DEP001");
        return request;
    }

    private MbTransferOnUsConfirmRequest createMockOnUsConfirmRequest() {
        MbTransferOnUsConfirmRequest request = new MbTransferOnUsConfirmRequest();
        request.setTransId("TXN001");
        return request;
    }

    private MbTransferPromptpayValidateRequest createMockPromptpayValidateRequest() {
        MbTransferPromptpayValidateRequest request = new MbTransferPromptpayValidateRequest();
        request.setFromAccountNo("**********");
        request.setToAccountNo("**********");
        request.setAmount("1000.00");
        request.setToBankCode("001");
        request.setToFavoriteName("Test PromptPay");
        request.setFlow("Setting");
        request.setNote("Test transfer");
        request.setCategoryId("1");
        request.setDepositNo("DEP001");
        request.setQr("test-qr-code");
        return request;
    }

    private MbTransferPromptpayConfirmRequest createMockPromptpayConfirmRequest() {
        MbTransferPromptpayConfirmRequest request = new MbTransferPromptpayConfirmRequest();
        request.setTransId("TXN001");
        request.setFrUuid("FR001");
        return request;
    }

    private DepositAccount createMockDepositAccount() {
        DepositAccount account = new DepositAccount();
        account.setAccountNumber("**********");
        account.setAvailableBalance(new BigDecimal("10000.00"));
        account.setAccountType("SA");
        account.setAccountStatus("ACTIVE");
        return account;
    }

    private TmbServiceResponse<TransferOffUsValidateResponse> createMockOffUsValidateResponse() {
        TransferOffUsValidateResponse response = new TransferOffUsValidateResponse();
        response.setTransId("TXN001");
        response.setAmount("1000.00");
        response.setFee("5.00");
        response.setToAccountName("Test Account");
        response.setIsRequireConfirmPin(false);

        TmbServiceResponse<TransferOffUsValidateResponse> serviceResponse = new TmbServiceResponse<>();
        serviceResponse.setData(response);
        return serviceResponse;
    }

    private TmbServiceResponse<TransferOffUsConfirmResponse> createMockOffUsConfirmResponse() {
        TransferOffUsConfirmResponse response = new TransferOffUsConfirmResponse();
        response.setReferenceNo("REF001");
        response.setRemainingBalance("9000.00");
        response.setIsToOwnAccount(false);
        response.setQr("test-qr");

        TmbServiceResponse<TransferOffUsConfirmResponse> serviceResponse = new TmbServiceResponse<>();
        serviceResponse.setData(response);
        return serviceResponse;
    }

    private TmbServiceResponse<TransferOnUsValidateResponse> createMockOnUsValidateResponse() {
        TransferOnUsValidateResponse response = new TransferOnUsValidateResponse();
        response.setTransId("TXN001");
        response.setAmount("1000.00");
        response.setFee("0.00");
        response.setToAccountName("Test Account");
        response.setIsRequireConfirmPin(false);

        TmbServiceResponse<TransferOnUsValidateResponse> serviceResponse = new TmbServiceResponse<>();
        serviceResponse.setData(response);
        return serviceResponse;
    }

    private TmbServiceResponse<TransferOnUsConfirmResponse> createMockOnUsConfirmResponse() {
        TransferOnUsConfirmResponse response = new TransferOnUsConfirmResponse();
        response.setReferenceNo("REF001");
        response.setRemainingBalance("9000.00");
        response.setIsToOwnAccount(true);

        TmbServiceResponse<TransferOnUsConfirmResponse> serviceResponse = new TmbServiceResponse<>();
        serviceResponse.setData(response);
        return serviceResponse;
    }

    private TmbServiceResponse<TransferPromptpayValidateResponse> createMockPromptpayValidateServiceResponse() {
        TransferPromptpayValidateResponse response = new TransferPromptpayValidateResponse();
        response.setTransId("Test");
        response.setAmount("1000.00");
        response.setFee("5.00");
        response.setToAccountName("Test PromptPay");
        response.setIsRequireConfirmPin(false);
        response.setIsRequireFr(false);
        response.setIsRequireCommonAuthen(false);

        TmbServiceResponse<TransferPromptpayValidateResponse> serviceResponse = new TmbServiceResponse<>();
        serviceResponse.setData(response);
        return serviceResponse;
    }

    private TmbServiceResponse<TransferPromptpayConfirmResponse> createMockPromptpayConfirmServiceResponse() {
        TransferPromptpayConfirmResponse response = new TransferPromptpayConfirmResponse();
        response.setReferenceNo("Test");
        response.setRemainingBalance("9000.00");
        response.setIsToOwnAccount(false);
        response.setTransferCreatedDatetime("2023-01-01T10:00:00");
        response.setQr("test-qr");

        TmbServiceResponse<TransferPromptpayConfirmResponse> serviceResponse = new TmbServiceResponse<>();
        serviceResponse.setData(response);
        return serviceResponse;
    }

    @NotNull
    @SuppressWarnings("SameParameterValue")
    private static String plusHours(int hours) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime plusOneHour = now.plusHours(hours);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        return plusOneHour.format(formatter);
    }

    @NotNull
    @SuppressWarnings("SameParameterValue")
    private static String minusHours(int hours) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime plusOneHour = now.minusHours(hours);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        return plusOneHour.format(formatter);
    }
}
