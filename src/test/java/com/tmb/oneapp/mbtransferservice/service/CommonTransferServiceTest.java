package com.tmb.oneapp.mbtransferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.CustomerProfileStatus;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.mbtransferservice.client.CustomerServiceClient;
import com.tmb.oneapp.mbtransferservice.client.PaymentExpServiceClient;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.V2DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferResponse;
import com.tmb.oneapp.mbtransferservice.model.fx.FXExchangeRateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.service.v2.V2AccountTransferService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class CommonTransferServiceTest {
    @Mock
    AccountTransferService accountTransferService;
    @Mock
    V2AccountTransferService v2AccountTransferService;
    @Mock
    PaymentExpServiceClient paymentExpServiceClient;
    @Mock
    CustomerServiceClient customerServiceClient;

    @InjectMocks
    CommonTransferServiceImpl commonTransferService;

    String crmId;
    String correlationId;
    String accountNumber;
    String toAccountNumber;
    String deviceId;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        accountNumber = "**********";
        toAccountNumber = "**********";
        deviceId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
    }

    @Test
    void testValidateAccountTransferWhenAccountExistingThenSuccess() throws TMBCommonException {
        Mockito.when(accountTransferService.getDepositAccountList(correlationId, crmId, false, false)).thenReturn(mockDepositAccount(Boolean.FALSE, false));
        try {
            DepositAccount dp = commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber);
            Mockito.verify(accountTransferService, Mockito.times(1)).getDepositAccountList(correlationId, crmId, false, false);
            Assertions.assertEquals(accountNumber, dp.getAccountNumber());
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }
    @Test
    void testValidateAccountTransferWhenAccountExistingButAvailableBalanceNotExistedShouldGetAccountBalanceThenSuccess() throws TMBCommonException {
        AccountBalanceTransferResponse accountBalanceTransferResponse = new AccountBalanceTransferResponse();
        accountBalanceTransferResponse.setAccountNumber(accountNumber);
        accountBalanceTransferResponse.setAvailableBalance(new BigDecimal("5000"));
        accountBalanceTransferResponse.setAccountStatus("AVAILABLE");
        accountBalanceTransferResponse.setLinkedAccount("LiNK");
        accountBalanceTransferResponse.setAccountName("TEST NAME");

        Mockito.when(accountTransferService.getDepositAccountList(correlationId, crmId, false, false)).thenReturn(mockDepositAccountWithoutAvailableBalance(Boolean.FALSE, false));
        Mockito.when(accountTransferService.getAccountBalance(Mockito.anyString(), any())).thenReturn(accountBalanceTransferResponse);
        try {
            DepositAccount dp = commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber);
            Mockito.verify(accountTransferService, Mockito.times(1)).getDepositAccountList(correlationId, crmId, false, false);
            Assertions.assertEquals(accountNumber, dp.getAccountNumber());
            Assertions.assertEquals(new BigDecimal("5000"), dp.getAvailableBalance());
            Assertions.assertEquals("AVAILABLE", dp.getAccountStatus());
            Assertions.assertEquals("LiNK", dp.getLinkedAccount());
            Assertions.assertEquals("TEST NAME", dp.getAccountName());
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testValidateAccountTransferWhenCustomerErrorThenThrowTMBCommonException() throws TMBCommonException {
        Mockito.when(accountTransferService.getDepositAccountList(correlationId, crmId, false, false)).thenThrow(TMBCommonException.class);
        Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber));
        Mockito.verify(accountTransferService, Mockito.times(1)).getDepositAccountList(correlationId, crmId, false, false);
    }

    @Test
    void testValidateAccountTransferWhenCustomerReturnNullThenThrowTMBCommonException() throws TMBCommonException {
        Mockito.when(accountTransferService.getDepositAccountList(correlationId, crmId, false, false)).thenReturn(Collections.emptyList());
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(),exception.getErrorCode());
        Mockito.verify(accountTransferService, Mockito.times(1)).getDepositAccountList(correlationId, crmId, false, false);
    }

    @Test
    void testValidateAccountTransferWhenCustomerReturnOnlyHiddenAccountThenThrowTMBCommonException() throws TMBCommonException {
        List<DepositAccountTransfer> response = new ArrayList<>();
        Mockito.when(accountTransferService.getDepositAccountList(correlationId, crmId, false, false)).thenReturn(response);
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(),exception.getErrorCode());
        Mockito.verify(accountTransferService, Mockito.times(1)).getDepositAccountList(correlationId, crmId, false, false);
    }

    @Test
    void testValidateAccountTransferWhenCustomerReturnIsViewOnlyThenThrowTMBCommonException() throws TMBCommonException {
        Mockito.when(accountTransferService.getDepositAccountList(correlationId, crmId, false, false)).thenReturn(mockDepositAccount(false, true));
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(),exception.getErrorCode());
        Mockito.verify(accountTransferService, Mockito.times(1)).getDepositAccountList(correlationId, crmId, false, false);
    }

    @Test
    void testValidateAccountTransferWhenCustomerReturnIsViewOnlyAndHideThenThrowTMBCommonException() throws TMBCommonException {
        Mockito.when(accountTransferService.getDepositAccountList(correlationId, crmId, false, false)).thenReturn(mockDepositAccount(true, true));
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(),exception.getErrorCode());
        Mockito.verify(accountTransferService, Mockito.times(1)).getDepositAccountList(correlationId, crmId, false, false);
    }

    @Test
    void testValidateAccountTransferWhenCustomerReturnAccountNotMatchFromAccountThenThrowTMBCommonException() throws TMBCommonException {
        List<DepositAccountTransfer> mocAcct = mockDepositAccount(false, false);
        mocAcct.get(0).setAccountNumber("********");
        Mockito.when(accountTransferService.getDepositAccountList(correlationId, crmId, false, false)).thenReturn(mocAcct);
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(),exception.getErrorCode());
        Mockito.verify(accountTransferService, Mockito.times(1)).getDepositAccountList(correlationId, crmId, false, false);
    }

    @Test
    void testCheckBalanceWhenInvalidParamThenThrowTMBCommonException(){
        DepositAccount dp = mockDepositAccount(Boolean.FALSE, false).stream().findFirst().orElse(new DepositAccountTransfer());
        Assertions.assertThrows(TMBCommonException.class,()-> commonTransferService.isOverAvailableBalance(dp.getAvailableBalance(), "AAA"));
    }

    @Test
    void testCheckBalanceWhenLessThanOrEqualAvailableAmountThenReturnFalse(){
        try {
            DepositAccount dp = mockDepositAccount(Boolean.FALSE, false).stream().findFirst().orElse(new DepositAccountTransfer());
            Boolean result = commonTransferService.isOverAvailableBalance(dp.getAvailableBalance(),"10001");
            Assertions.assertTrue(result);
            result = commonTransferService.isOverAvailableBalance(dp.getAvailableBalance(),"10000");
            Assertions.assertFalse(result);
            result = commonTransferService.isOverAvailableBalance(dp.getAvailableBalance(),"9999");
            Assertions.assertFalse(result);
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testGetCrmFromDeviceIdWhenCrmNotEmptyThenSuccess() throws TMBCommonException {
        String result = commonTransferService.getCrmIdFromDeviceId(crmId, deviceId);
        Assertions.assertEquals(crmId,result);
    }

    @Test
    void testGetCrmFromDeviceIdWhenCrmEmptyThenSuccess() throws TMBCommonException {

        CustomerProfileStatus status = new CustomerProfileStatus();
        status.setCrmId(crmId);
        status.setMbUserStatusId(CommonConstant.MB_CUSTOMER_STATUS);
        status.setEbCustomerStatusId(CommonConstant.EB_CUSTOMER_STATUS);
        Mockito.when(customerServiceClient.getCrmIdFromDeviceId(deviceId)).thenReturn(mockResponseEntityOk(status));

        String result = commonTransferService.getCrmIdFromDeviceId("", deviceId);
        Assertions.assertEquals(crmId,result);
        Mockito.verify(customerServiceClient,Mockito.times(1)).getCrmIdFromDeviceId(deviceId);
    }

    @Test
    void testGetCrmFromDeviceIdWhenUserPinLockThenThrowTMBCommonException() {

        CustomerProfileStatus status = new CustomerProfileStatus();
        status.setCrmId(crmId);
        status.setMbUserStatusId(CommonConstant.MB_CUSTOMER_STATUS_PIN_LOCK);
        status.setEbCustomerStatusId(CommonConstant.EB_CUSTOMER_STATUS);
        Mockito.when(customerServiceClient.getCrmIdFromDeviceId(deviceId)).thenReturn(mockResponseEntityOk(status));

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService.getCrmIdFromDeviceId("", deviceId));
        Assertions.assertEquals(ResponseCode.PIN_ERROR_LOCKED_CAUSE.getCode(),exception.getErrorCode());
        Mockito.verify(customerServiceClient,Mockito.times(1)).getCrmIdFromDeviceId(deviceId);
    }

    @Test
    void testGetCrmFromDeviceIdWhenUserInvalidThenThrowTMBCommonException() {

        CustomerProfileStatus status = new CustomerProfileStatus();
        status.setCrmId(crmId);
        status.setMbUserStatusId("555");
        status.setEbCustomerStatusId("555");
        Mockito.when(customerServiceClient.getCrmIdFromDeviceId(deviceId)).thenReturn(mockResponseEntityOk(status));

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService.getCrmIdFromDeviceId("", deviceId));
        Assertions.assertEquals(ResponseCode.FAILED.getCode(),exception.getErrorCode());
        Mockito.verify(customerServiceClient,Mockito.times(1)).getCrmIdFromDeviceId(deviceId);
    }

    private <T> ResponseEntity<TmbOneServiceResponse<T>> mockResponseEntityOk(T data) {
        TmbOneServiceResponse<T> res = new TmbOneServiceResponse<>();
        res.setData(data);
        res.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        return ResponseEntity.ok(res);
    }

    private List<DepositAccountTransfer> mockDepositAccount(boolean isHidden, boolean isViewOnly) {
        DepositAccountTransfer dp = new DepositAccountTransfer();
        dp.setAccountName("Test");
        dp.setAccountNumber(accountNumber);
        dp.setAccountType("Test");
        dp.setAvailableBalance(new BigDecimal("10000"));
        dp.setHideAccountFlag(isHidden);
        dp.setViewOnlyFlag(isViewOnly);
        return List.of(dp);
    }

    @SuppressWarnings("SameParameterValue")
    private List<DepositAccountTransfer> mockDepositAccountWithoutAvailableBalance(boolean isHidden, boolean isViewOnly) {
        DepositAccountTransfer dp = new DepositAccountTransfer();
        dp.setAccountName("Test");
        dp.setAccountNumber(accountNumber);
        dp.setAccountType("Test");
        dp.setHideAccountFlag(isHidden);
        dp.setViewOnlyFlag(isViewOnly);
        return List.of(dp);
    }

    @Test
    void testValidateAccountTransfer2WhenAccountExistingThenSuccess() throws TMBCommonException {
        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        v2DepositAccountTransfer.setAccountList(mockDepositAccount(Boolean.FALSE, false));
        Mockito.when(v2AccountTransferService.getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        try {
            DepositAccount dp = commonTransferService
                    .validateAccountTransfer(crmId, correlationId, getMbTransferOnUsValidateRequest());
            Mockito.verify(v2AccountTransferService, Mockito.times(1))
                    .getDepositAccountList(correlationId, crmId, false, false, null);
            Assertions.assertEquals(accountNumber, dp.getAccountNumber());
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    private MbTransferOnUsValidateRequest getMbTransferOnUsValidateRequest() {
        MbTransferOnUsValidateRequest request = new MbTransferOnUsValidateRequest();
        request.setToAccountNo(toAccountNumber);
        request.setFromAccountNo(accountNumber);
        request.setAmount("500");
        return request;
    }

    @Test
    void testValidateAccountTransfer2WhenAccountExistingButAvailableBalanceNotExistedShouldGetAccountBalanceThenSuccess() throws TMBCommonException {
        AccountBalanceTransferResponse accountBalanceTransferResponse = new AccountBalanceTransferResponse();
        accountBalanceTransferResponse.setAccountNumber(accountNumber);
        accountBalanceTransferResponse.setAvailableBalance(new BigDecimal("5000"));
        accountBalanceTransferResponse.setAccountStatus("AVAILABLE");
        accountBalanceTransferResponse.setLinkedAccount("LiNK");
        accountBalanceTransferResponse.setAccountName("TEST NAME");

        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        v2DepositAccountTransfer
                .setAccountList(mockDepositAccountWithoutAvailableBalance(Boolean.FALSE, false));
        Mockito.when(v2AccountTransferService
                        .getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        Mockito.lenient().when(v2AccountTransferService
                        .getAccountBalance(Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), any()))
                .thenReturn(accountBalanceTransferResponse);

        Mockito.lenient().when(accountTransferService
                        .getAccountBalance(Mockito.anyString(), any()))
                .thenReturn(accountBalanceTransferResponse);
        try {
            DepositAccount dp = commonTransferService
                    .validateAccountTransfer(crmId, correlationId, getMbTransferOnUsValidateRequest());
            Mockito.verify(v2AccountTransferService, Mockito.times(1))
                    .getDepositAccountList(correlationId, crmId, false, false, null);
            Assertions.assertEquals(accountNumber, dp.getAccountNumber());
            Assertions.assertEquals(new BigDecimal("5000"), dp.getAvailableBalance());
            Assertions.assertEquals("AVAILABLE", dp.getAccountStatus());
            Assertions.assertEquals("LiNK", dp.getLinkedAccount());
            Assertions.assertEquals("TEST NAME", dp.getAccountName());
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testValidateAccountTransfer2WhenCustomerErrorThenThrowTMBCommonException() throws TMBCommonException {
        Mockito.when(v2AccountTransferService
                .getDepositAccountList(correlationId, crmId, false, false, null))
                .thenThrow(TMBCommonException.class);
        Assertions.assertThrows(TMBCommonException.class, () ->
                commonTransferService.validateAccountTransfer(crmId, correlationId, getMbTransferOnUsValidateRequest()));
        Mockito.verify(v2AccountTransferService, Mockito.times(1))
                .getDepositAccountList(correlationId, crmId, false, false, null);
    }

    @Test
    void testValidateAccountTransfer2WhenCustomerReturnNullThenThrowTMBCommonException()
            throws TMBCommonException {
        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        v2DepositAccountTransfer.setAccountList(Collections.emptyList());
        Mockito.when(v2AccountTransferService
                        .getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                commonTransferService.validateAccountTransfer(crmId, correlationId, getMbTransferOnUsValidateRequest()));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(),exception.getErrorCode());
        Mockito.verify(v2AccountTransferService, Mockito.times(1))
                .getDepositAccountList(correlationId, crmId, false, false, null);
    }

    @Test
    void testValidateAccountTransfer2WhenCustomerReturnOnlyHiddenAccountThenThrowTMBCommonException()
            throws TMBCommonException {
        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        v2DepositAccountTransfer.setAccountList(new ArrayList<>());
        Mockito.when(v2AccountTransferService
                        .getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                commonTransferService.validateAccountTransfer(crmId, correlationId, getMbTransferOnUsValidateRequest()));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(),exception.getErrorCode());
        Mockito.verify(v2AccountTransferService, Mockito.times(1))
                .getDepositAccountList(correlationId, crmId, false, false, null);
    }

    @Test
    void testValidateAccountTransfer2WhenCustomerReturnIsViewOnlyThenThrowTMBCommonException()
            throws TMBCommonException {
        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        v2DepositAccountTransfer.setAccountList(mockDepositAccount(false, true));
        Mockito.when(v2AccountTransferService
                        .getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                commonTransferService.validateAccountTransfer(crmId, correlationId, getMbTransferOnUsValidateRequest()));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(),exception.getErrorCode());
        Mockito.verify(v2AccountTransferService, Mockito.times(1))
                .getDepositAccountList(correlationId, crmId, false, false, null);
    }

    @Test
    void testValidateAccountTransfer2WhenCustomerReturnIsViewOnlyAndHideThenThrowTMBCommonException()
            throws TMBCommonException {
        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        v2DepositAccountTransfer.setAccountList(mockDepositAccount(true, true));
        Mockito.when(v2AccountTransferService
                .getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () ->
                commonTransferService.validateAccountTransfer(crmId, correlationId, getMbTransferOnUsValidateRequest()));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(),exception.getErrorCode());
        Mockito.verify(v2AccountTransferService, Mockito.times(1))
                .getDepositAccountList(correlationId, crmId, false, false, null);
    }

    @Test
    void testValidateAccountTransfer2WhenCustomerReturnAccountNotMatchFromAccountThenThrowTMBCommonException()
            throws TMBCommonException {

        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        List<DepositAccountTransfer> mocAcct = mockDepositAccount(false, false);
        mocAcct.get(0).setAccountNumber("********");
        v2DepositAccountTransfer.setAccountList(mocAcct);
        Mockito.when(v2AccountTransferService
                .getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService
                .validateAccountTransfer(crmId, correlationId, getMbTransferOnUsValidateRequest()));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(),exception.getErrorCode());
        Mockito.verify(v2AccountTransferService, Mockito.times(1))
                .getDepositAccountList(correlationId, crmId, false, false, null);
    }

    @Test
    void testValidateAccountTransferFCDCurrencyFailed() throws TMBCommonException {
        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        v2DepositAccountTransfer.setAccountList(List.of());
        List<DepositAccountTransfer> fromDepositAccount = mockDepositAccount(Boolean.FALSE, false);
        fromDepositAccount.get(0).setAccountNumber("**********").setAcctCtl2("0002").setAccountType("SDA");
        List<DepositAccountTransfer> toDepositAccount = new ArrayList<>(mockDepositAccount(Boolean.FALSE, false));
        toDepositAccount.get(0).setAccountNumber("**********").setAcctCtl2("0004").setAccountType("SDA");
        toDepositAccount.addAll(fromDepositAccount);
        v2DepositAccountTransfer.setFcdAccountList(toDepositAccount);
        Mockito.when(v2AccountTransferService.getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        TmbServiceResponse<FXExchangeRateResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new FXExchangeRateResponse());
        Mockito.when(paymentExpServiceClient.getExchangeRates(any(), any()))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));
        MbTransferOnUsValidateRequest request = new MbTransferOnUsValidateRequest();
        request.setToAccountNo("**********");
        request.setFromAccountNo("**********");
        request.setAmount("500");
        TMBCommonException exception =  Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService
                    .validateAccountTransfer(crmId, correlationId, request));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateAccountTransferFCDSuccess() throws TMBCommonException {
        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        v2DepositAccountTransfer.setAccountList(List.of());
        List<DepositAccountTransfer> fromDepositAccount = mockDepositAccount(Boolean.FALSE, false);
        fromDepositAccount.get(0).setAccountNumber("**********").setAcctCtl2("0002").setAccountType("SDA")
                .setAvailableBalance(null);
        List<DepositAccountTransfer> toDepositAccount = new ArrayList<>(mockDepositAccount(Boolean.FALSE, false));
        toDepositAccount.get(0).setAccountNumber("**********").setAcctCtl2("0002").setAccountType("CDA");
        toDepositAccount.addAll(fromDepositAccount);
        v2DepositAccountTransfer.setFcdAccountList(toDepositAccount);
        Mockito.when(v2AccountTransferService.getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        Mockito.when(v2AccountTransferService.getAccountBalance(any(), any(), any(), any()))
                .thenReturn(new AccountBalanceTransferResponse().setAvailableBalance(BigDecimal.ZERO));
        TmbServiceResponse<FXExchangeRateResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new FXExchangeRateResponse());
        Mockito.when(paymentExpServiceClient.getExchangeRates(any(), any()))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));
        MbTransferOnUsValidateRequest request = new MbTransferOnUsValidateRequest();
        request.setFromAccountNo("**********");
        request.setToAccountNo("**********");
        request.setAmount("500");
        Assertions.assertDoesNotThrow(() -> commonTransferService
                .validateAccountTransfer(crmId, correlationId, request));
    }

    @Test
    void checkIsNonWorkingHour() throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss", Locale.ENGLISH);

        Date date = formatter.parse("05-05-2021 10:15:55");
        boolean result = commonTransferService.isNonWorkingHour("22:55","06:00", date);
        Assertions.assertFalse(result);

        Date date2 = formatter.parse("05-05-2021 07:15:55");
        boolean result2 = commonTransferService.isNonWorkingHour("22:55", "06:00",date2);
        Assertions.assertFalse(result2);

        Date date3 = formatter.parse("05-05-2021 22:15:55");
        boolean result3 = commonTransferService.isNonWorkingHour("22:55", "06:00",date3);
        Assertions.assertFalse(result3);

        Date date4 = formatter.parse("05-05-2021 22:56:55");
        boolean result4 = commonTransferService.isNonWorkingHour("22:55","06:00",date4);
        Assertions.assertTrue(result4);

        Date date5 = formatter.parse("06-05-2021 05:15:55");
        boolean result5 = commonTransferService.isNonWorkingHour("22:55","06:00",date5);
        Assertions.assertTrue(result5);

        Date date6 = formatter.parse("06-05-2021 05:15:55");
        boolean result6 = commonTransferService.isNonWorkingHour("18:00","21:00", date6);
        Assertions.assertFalse(result6);

        Date date7 = formatter.parse("06-05-2021 18:15:55");
        boolean result7 = commonTransferService.isNonWorkingHour("18:00","21:00",date7);
        Assertions.assertTrue(result7);

        Date date8 = formatter.parse("06-05-2021 20:00:55");
        boolean result8 = commonTransferService.isNonWorkingHour("18:00","21:00", date8);
        Assertions.assertTrue(result8);

        Date date9 = formatter.parse("06-05-2021 22:15:30");
        boolean result9 = commonTransferService.isNonWorkingHour("18:00","21:00",date9);
        Assertions.assertFalse(result9);
    }

    @Test
    void testValidateAccountTransferFcdTdNotOwner() throws TMBCommonException {
        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        v2DepositAccountTransfer.setAccountList(List.of());
        List<DepositAccountTransfer> fromDepositAccount = mockDepositAccount(Boolean.FALSE, false);
        fromDepositAccount.get(0).setAccountNumber("**********").setAcctCtl2("0002").setAccountType("CDA");
        List<DepositAccountTransfer> toDepositAccount = new ArrayList<>(mockDepositAccount(Boolean.FALSE, false));
        toDepositAccount.get(0).setAccountNumber("**********").setAcctCtl2("0002").setAccountType("SDA");
        toDepositAccount.addAll(fromDepositAccount);
        v2DepositAccountTransfer.setFcdAccountList(toDepositAccount);
        Mockito.when(v2AccountTransferService.getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        TmbServiceResponse<FXExchangeRateResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new FXExchangeRateResponse());
        Mockito.when(paymentExpServiceClient.getExchangeRates(any(), any()))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));
        MbTransferOnUsValidateRequest request = new MbTransferOnUsValidateRequest();
        request.setToAccountNo("**********");
        request.setFromAccountNo("**********");
        request.setAmount("500");
        TMBCommonException exception =  Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService
                .validateAccountTransfer(crmId, correlationId, request));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateAccountTransferFcdTdToTd() throws TMBCommonException {
        V2DepositAccountTransfer v2DepositAccountTransfer = new V2DepositAccountTransfer();
        v2DepositAccountTransfer.setAccountList(List.of());
        List<DepositAccountTransfer> fromDepositAccount = mockDepositAccount(Boolean.FALSE, false);
        fromDepositAccount.get(0).setAccountNumber("**********").setAcctCtl2("0002").setAccountType("CDA");
        List<DepositAccountTransfer> toDepositAccount = new ArrayList<>(mockDepositAccount(Boolean.FALSE, false));
        toDepositAccount.get(0).setAccountNumber("**********").setAcctCtl2("0002").setAccountType("CDA");
        toDepositAccount.addAll(fromDepositAccount);
        v2DepositAccountTransfer.setFcdAccountList(toDepositAccount);
        Mockito.when(v2AccountTransferService.getDepositAccountList(correlationId, crmId, false, false, null))
                .thenReturn(v2DepositAccountTransfer);
        TmbServiceResponse<FXExchangeRateResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new FXExchangeRateResponse());
        Mockito.when(paymentExpServiceClient.getExchangeRates(any(), any()))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));
        MbTransferOnUsValidateRequest request = new MbTransferOnUsValidateRequest();
        request.setToAccountNo("**********");
        request.setFromAccountNo("**********");
        request.setAmount("500");
        TMBCommonException exception =  Assertions.assertThrows(TMBCommonException.class, () -> commonTransferService
                .validateAccountTransfer(crmId, correlationId, request));
        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(), exception.getErrorCode());
    }
}
