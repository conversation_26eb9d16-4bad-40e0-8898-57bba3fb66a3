package com.tmb.oneapp.mbtransferservice.service.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.mbtransferservice.client.TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.client.v2.V2TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.model.CommonTime;
import com.tmb.oneapp.mbtransferservice.model.ServiceHourResponse;
import com.tmb.oneapp.mbtransferservice.model.TransferModuleModel;
import com.tmb.oneapp.mbtransferservice.service.CommonTransferService;
import feign.FeignException;
import jakarta.validation.constraints.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class V2TransferServiceImplTest {

    @InjectMocks
    V2TransferServiceImpl v2TransferService;

    @Mock
    V2TransferServiceClient transferServiceClient;
    @Mock
    TransferServiceClient transferClient;
    @Mock
    CommonTransferService commonTransferService;

    @Test
    void validateTdCutoffTime() throws TMBCommonException {

        CommonTime commonTime = new CommonTime();
        commonTime.setStart(plusHours(1));
        commonTime.setEnd(plusHours(2));
        CommonTime commonTime2 = new CommonTime();
        commonTime.setStart(minusHours(1));
        commonTime.setEnd(plusHours(1));

        TmbOneServiceResponse<TransferModuleModel> response = new TmbOneServiceResponse<>();
        response.setData(TransferModuleModel.builder().withdrawTdCutoffTime(commonTime).build());
        TmbOneServiceResponse<TransferModuleModel> response2 = new TmbOneServiceResponse<>();
        response2.setData(TransferModuleModel.builder().withdrawTdCutoffTime(commonTime2).build());
        when(transferClient.getAccountConfiguration(anyString()))
                .thenReturn(ResponseEntity.ok(response))
                .thenReturn(ResponseEntity.ok(response2))
                .thenThrow(FeignException.class);
        when(commonTransferService.isNonWorkingHour(anyString(),anyString(),any()))
                .thenReturn(true)
                .thenReturn(false);
        ServiceHourResponse serviceHourResponse =
                v2TransferService.validateTdCutoffTime("32fbd3b2-3f97-4a89-ar39-b4f628fbc8da");
        assertTrue(serviceHourResponse.isNonServiceHour());
        serviceHourResponse =
                v2TransferService.validateTdCutoffTime("32fbd3b2-3f97-4a89-ar39-b4f628fbc8da");
        assertFalse(serviceHourResponse.isNonServiceHour());
        assertThrows(TMBCommonException.class, () ->
                v2TransferService.validateTdCutoffTime("32fbd3b2-3f97-4a89-ar39-b4f628fbc8da"));
    }

    @NotNull
    @SuppressWarnings("SameParameterValue")
    private static String plusHours(int hours) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime plusOneHour = now.plusHours(hours);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        return plusOneHour.format(formatter);
    }

    @NotNull
    @SuppressWarnings("SameParameterValue")
    private static String minusHours(int hours) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime plusOneHour = now.minusHours(hours);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("HH:mm");
        return plusOneHour.format(formatter);
    }

}