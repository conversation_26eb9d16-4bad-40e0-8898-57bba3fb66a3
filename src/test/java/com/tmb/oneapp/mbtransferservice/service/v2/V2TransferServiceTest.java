package com.tmb.oneapp.mbtransferservice.service.v2;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithoutMappingPhrases;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.mbtransferservice.client.TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.client.v2.V2TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.CommonTime;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.TransferModuleModel;
import com.tmb.oneapp.mbtransferservice.model.offus.MbTransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.TransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.service.CommonTransferServiceImpl;
import feign.FeignException;
import feign.Request;
import feign.Response;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Objects;

import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_DEVICE_MODEL;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.HEADER_IP_ADDRESS;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.TRANSFER_ERROR_TTB_CBS_PREFIX;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class V2TransferServiceTest {

    @Mock
    CommonTransferServiceImpl commonTransferService;
    @Mock
    V2TransferServiceClient v2TransferServiceClient;
    @Mock
    TransferServiceClient transferServiceClient;
    @InjectMocks
    V2TransferServiceImpl v2TransferService;

    String crmId;
    String correlationId;
    String accountNumber;
    BigDecimal avAmount;
    String deviceId;
    String falsePreLogin;
    String appVersion;
    String deviceModel;
    String timeStamp;
    Boolean isRequireFr;
    String ipAddress;
    HttpHeaders headers = new HttpHeaders();


    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        deviceId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        appVersion = "01";
        deviceModel = "01";
        timeStamp = "yyyy-MM-dd'T'HH:mm:ss";
        accountNumber = "**********";
        avAmount = new BigDecimal("10000");
        falsePreLogin = "false";
        isRequireFr = Boolean.FALSE;
        ipAddress = "**********";
        headers.set(HEADER_IP_ADDRESS, ipAddress);
        headers.set(HEADER_APP_VERSION,appVersion);
        headers.set(HEADER_DEVICE_MODEL, deviceModel);
        headers.set(HEADER_APP_VERSION, appVersion);
    }


    @Test
    void testOffUsValidateWhenValidRequestThenSuccess() throws TMBCommonException {
        String requestAmt = "10000";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber)).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(false);
        Mockito.when(v2TransferServiceClient.offUsValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferOffUsValidateRequest.class))).thenReturn(mockResponseEntityOk(mockOffUsValidateResponse()));
        try {
            TmbServiceResponse<TransferOffUsValidateResponse> res = v2TransferService.offUsValidate(correlationId, crmId, headers, mockMbOffUsValidateRequest(requestAmt));
            Assertions.assertEquals(ResponseCode.SUCCESS.getCode(),res.getStatus().getCode());
            Assertions.assertEquals(Boolean.FALSE, res.getData().getIsRequireFr());
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId, accountNumber);
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);
            Mockito.verify(v2TransferServiceClient, Mockito.times(1)).offUsValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferOffUsValidateRequest.class));

        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testOffUsConfirmWhenValidRequestThenSuccess() throws TMBCommonException {
        Mockito.when(v2TransferServiceClient.offUsConfirm(Mockito.eq(correlationId), Mockito.eq(crmId),Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), anyString(), Mockito.any(TransferOffUsConfirmRequest.class))).thenReturn(mockResponseEntityOk(mockOffUsConfirmResponse()));
        TmbServiceResponse<TransferOffUsConfirmResponse> res = v2TransferService.offUsConfirm(correlationId, crmId, "","",headers, mockOffUsConfirmRequest());
        Assertions.assertEquals(ResponseCode.SUCCESS.getCode(),res.getStatus().getCode());
        Mockito.verify(v2TransferServiceClient, Mockito.times(1)).offUsConfirm(Mockito.eq(correlationId), Mockito.eq(crmId),Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), anyString(), Mockito.any(TransferOffUsConfirmRequest.class));
    }

    @Test
    void testOffUsConfirmWhenReturnNullThenThrowTMBCommonException() {
        Mockito.when(v2TransferServiceClient.offUsConfirm(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), anyString(), Mockito.any(TransferOffUsConfirmRequest.class))).thenReturn(ResponseEntity.ok(null));
        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> v2TransferService.offUsConfirm(correlationId, crmId, "","",headers, mockOffUsConfirmRequest()));
        Assertions.assertEquals(ResponseCode.FAILED.getCode(),exception.getErrorCode());
        Mockito.verify(v2TransferServiceClient, Mockito.times(1)).offUsConfirm(Mockito.eq(correlationId), Mockito.eq(crmId),Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), anyString(), Mockito.any(TransferOffUsConfirmRequest.class));
    }

    @Test
    void testOffUsConfirmWhenTransferReturnBadRequestThenThrowTMBCommonException() {
        Mockito.when(v2TransferServiceClient.offUsConfirm(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), anyString(), Mockito.any(TransferOffUsConfirmRequest.class))).thenThrow(mockFeignException("Not Found","v1/transfer-service/off-us/confirm", Request.HttpMethod.POST,400,null));

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> v2TransferService.offUsConfirm(correlationId, crmId, "","",headers, mockOffUsConfirmRequest()));
        Assertions.assertEquals(ResponseCode.FAILED.getCode(),exception.getErrorCode());
        Mockito.verify(v2TransferServiceClient, Mockito.times(1)).offUsConfirm(Mockito.eq(correlationId), Mockito.eq(crmId),Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), anyString(), Mockito.any(TransferOffUsConfirmRequest.class));

    }

    @Test
    void testOffUsValidateWhenTransferReturnNullThenThrowTMBCommonException() throws TMBCommonException {
        String requestAmt = "10000";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber)).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(false);
        Mockito.when(v2TransferServiceClient.offUsValidate(Mockito.eq(correlationId), Mockito.eq(crmId),Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferOffUsValidateRequest.class))).thenReturn(ResponseEntity.ok(null));
        try {
            TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.offUsValidate(correlationId, crmId, headers, mockMbOffUsValidateRequest(requestAmt)));
            Assertions.assertEquals(ResponseCode.FAILED.getCode(),exception.getErrorCode());
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId, accountNumber);
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);
            Mockito.verify(v2TransferServiceClient, Mockito.times(1)).offUsValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferOffUsValidateRequest.class));

        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testOffUsValidateWhenTransferReturnNotFoundThenThrowTMBCommonException() throws TMBCommonException {
        String requestAmt = "10000";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber)).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(false);
        Mockito.when(v2TransferServiceClient.offUsValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferOffUsValidateRequest.class))).thenThrow(mockFeignException("Not Found","v1/transfer-service/off-us/validate",Request.HttpMethod.POST,404,null));

        try {
            TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.offUsValidate(correlationId, crmId, headers, mockMbOffUsValidateRequest(requestAmt)));
            Assertions.assertEquals(ResponseCode.FAILED.getCode(),exception.getErrorCode());
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId, accountNumber);
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);
            Mockito.verify(v2TransferServiceClient, Mockito.times(1)).offUsValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferOffUsValidateRequest.class));

        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testOffUsValidateWhenTransferReturn500WithDescriptionThenThrowTMBCommonException() throws TMBCommonException, JsonProcessingException {
        String requestAmt = "10000";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber)).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(false);
        TmbServiceResponse<Object> response = getObjectTmbServiceResponse();
        Mockito.when(v2TransferServiceClient.offUsValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferOffUsValidateRequest.class))).thenThrow(mockFeignException("Internal Server Error","v1/transfer-service/off-us/validate",Request.HttpMethod.POST,500, TMBUtils.convertJavaObjectToString(response)));

        try {
            TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.offUsValidate(correlationId, crmId, headers, mockMbOffUsValidateRequest(requestAmt)));
            Assertions.assertEquals(ResponseCode.FAILED.getCode(),exception.getErrorCode());
            Assertions.assertEquals("mb-transfer-service",exception.getService());
            Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR,exception.getStatus());
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId, accountNumber);
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);
            Mockito.verify(v2TransferServiceClient, Mockito.times(1)).offUsValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferOffUsValidateRequest.class));

        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    private static TmbServiceResponse<Object> getObjectTmbServiceResponse() {
        TmbServiceResponse<Object> response = new TmbServiceResponse<>();
        Description description = new Description();
        description.setEn("We cannot proceed your request right now. Please contact 1428 for more information. Sorry for the inconvenience.");
        description.setTh("ไม่สามารถทำรายการได้ ธนาคารขออภัยในความไม่สะดวกมา ณ ที่นี้  สอบถามเพิ่มเติม โทร. 1428");
        response.setStatus(new Status(ResponseCode.FAILED.getCode(), ResponseCode.FAILED.getMessage(), "transfer-service", description));
        return response;
    }

    @Test
    void testOffUsValidateWhenTransferReturn200ButFailThenThrowTMBCommonException() throws TMBCommonException {
        String requestAmt = "10000";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber)).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(false);
        Mockito.when(v2TransferServiceClient.offUsValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferOffUsValidateRequest.class))).thenReturn(mockResponseEntityOk(null,ResponseCode.INVALID_REQUEST));

        try {
            TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.offUsValidate(correlationId, crmId, headers, mockMbOffUsValidateRequest(requestAmt)));
            Assertions.assertEquals(ResponseCode.INVALID_REQUEST.getCode(),exception.getErrorCode());
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId, accountNumber);
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);
            Mockito.verify(v2TransferServiceClient, Mockito.times(1)).offUsValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferOffUsValidateRequest.class));

        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testOffUsValidateWhenOverBalanceThenThrowsTMBCommonException() throws TMBCommonException {
        String requestAmt = "10001";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber)).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(true);
        try {
            TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> v2TransferService.offUsValidate(correlationId, crmId, new HttpHeaders(), mockMbOffUsValidateRequest(requestAmt)));
            Assertions.assertEquals(ResponseCode.FAILED.getCode(),exception.getErrorCode());
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId, accountNumber);
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);

        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testOffUsValidateWhenIsViewOnlyThenThrowsTMBCommonException() throws TMBCommonException {
        String requestAmt = "10001";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, accountNumber)).thenReturn(mockDepositAccount(true));
        try {
            TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> v2TransferService.offUsValidate(correlationId, crmId, new HttpHeaders(), mockMbOffUsValidateRequest(requestAmt)));
            Assertions.assertEquals(ResponseCode.FAILED.getCode(),exception.getErrorCode());
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId, accountNumber);
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);

        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @SuppressWarnings("SameParameterValue")
    private FeignException mockFeignException(String message, String url,
                                              Request.HttpMethod httpMethod,
                                              int httpStatus,
                                              String responseText){
        return FeignException.errorStatus(
                message,
                Response.builder()
                        .status(httpStatus)
                        .headers(new HashMap<>())
                        .body(responseText, StandardCharsets.UTF_8)
                        .request(Request.create(
                                httpMethod,
                                url,
                                new HashMap<>(),
                                null,
                                null,
                                null)).build());
    }

    private <T> ResponseEntity<TmbServiceResponse<T>> mockResponseEntityOk(T data) {
        return mockResponseEntityOk(data,ResponseCode.SUCCESS);
    }

    private <T> ResponseEntity<TmbServiceResponse<T>> mockResponseEntityOk(T data,ResponseCode response) {
        TmbServiceResponse<T> res = new TmbServiceResponse<>();
        res.setData(data);
        res.setStatus(new Status(response.getCode(), response.getMessage(), response.getService(), new Description("en", "th")));
        return ResponseEntity.ok(res);
    }

    private TransferOffUsConfirmResponse mockOffUsConfirmResponse(){
        TransferOffUsConfirmResponse res = new TransferOffUsConfirmResponse();
        res.setReferenceNo("test1");
        return res;
    }

    private TransferOffUsConfirmRequest mockOffUsConfirmRequest(){
        TransferOffUsConfirmRequest req = new TransferOffUsConfirmRequest();
        req.setTransId("test1");
        return req;
    }
    private TransferOffUsValidateResponse mockOffUsValidateResponse() {
        TransferOffUsValidateResponse res = new TransferOffUsValidateResponse();
        res.setAmount(avAmount.toString());
        res.setIsRequireFr(Boolean.FALSE);
        return res;
    }

    private MbTransferOffUsValidateRequest mockMbOffUsValidateRequest(String amt) {
        MbTransferOffUsValidateRequest req = new MbTransferOffUsValidateRequest();
        req.setAmount(amt);
        req.setFromAccountNo(accountNumber);
        return req;
    }

    private DepositAccount mockDepositAccount(boolean isViewOnly) {
        DepositAccount dp = new DepositAccount();
        dp.setAccountName("Test");
        dp.setAccountNumber(accountNumber);
        dp.setAccountType("Test");
        dp.setAvailableBalance(avAmount);
        dp.setHideAccountFlag(Boolean.FALSE);
        dp.setViewOnlyFlag(isViewOnly);
        return dp;
    }

    private ResponseEntity<TmbServiceResponse<TransferPromptpayValidateResponse>> mockTransferPromptpayValidate(){
        TransferPromptpayValidateResponse res = new TransferPromptpayValidateResponse();
        res.setAmount("10");
        res.setFee("5");
        res.setTransId("1");
        res.setIsRequireConfirmPin(true);
        res.setToAccountName("acc");
        res.setIsRequireFr(Boolean.FALSE);

        return mockResponseEntityOk(res);
    }

    private MbTransferPromptpayValidateRequest mockPromptpayValidateRequest(String amt){
        MbTransferPromptpayValidateRequest req = new MbTransferPromptpayValidateRequest();
        req.setFromAccountNo("Test");
        req.setToAccountNo("Test");
        req.setToBankCode("Test");
        req.setToFavoriteName("Test");
        req.setAmount(amt);
        req.setCategoryId("Test");
        req.setNote("Test");
        req.setFlow("Flow");
        req.setQr("QR");
        req.setDepositNo("DepositNo");
        return req;
    }

    @Test
    void testPromptpayValidate() throws TMBCommonException{
        String requestAmt = "10000";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, "Test")).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(false);
        Mockito.when(commonTransferService.getCrmIdFromDeviceId(crmId,deviceId)).thenReturn(crmId);
        Mockito.when(v2TransferServiceClient.promptpayValidate(anyString(),anyString(),anyString(),anyString(),any(),anyString(),Mockito.any(TransferPromptpayValidateRequest.class))).thenReturn(mockTransferPromptpayValidate());
        try {
            MbTransferPromptpayValidateResponse res = v2TransferService.promptpayValidate(correlationId, crmId, deviceId, falsePreLogin, headers, mockPromptpayValidateRequest(requestAmt));
            Assertions.assertEquals(Boolean.FALSE, res.getIsRequireFr());
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId,"Test");
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);
            Mockito.verify(commonTransferService, Mockito.times(1)).getCrmIdFromDeviceId(crmId,deviceId);
            Mockito.verify(v2TransferServiceClient, Mockito.times(1)).promptpayValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(deviceId), Mockito.eq(falsePreLogin), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferPromptpayValidateRequest.class));
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testPromptpayValidateTransferNull() throws TMBCommonException{
        String requestAmt = "10000";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, "Test")).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(false);
        Mockito.when(commonTransferService.getCrmIdFromDeviceId(crmId,deviceId)).thenReturn(crmId);
        Mockito.when(v2TransferServiceClient.promptpayValidate(anyString(),anyString(),anyString(),anyString(), any(), anyString(), Mockito.any(TransferPromptpayValidateRequest.class))).thenReturn(null);
        try {
            Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.promptpayValidate(correlationId, crmId, deviceId, falsePreLogin, headers, mockPromptpayValidateRequest(requestAmt)));
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId,"Test");
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);
            Mockito.verify(commonTransferService, Mockito.times(1)).getCrmIdFromDeviceId(crmId,deviceId);
            Mockito.verify(v2TransferServiceClient, Mockito.times(1)).promptpayValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(deviceId), Mockito.eq(falsePreLogin), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferPromptpayValidateRequest.class));
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testPromptpayValidateTransferException() throws TMBCommonException{
        String requestAmt = "10000";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, "Test")).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(false);
        Mockito.when(commonTransferService.getCrmIdFromDeviceId(crmId,deviceId)).thenReturn(crmId);
        Mockito.when(v2TransferServiceClient.promptpayValidate(anyString(),anyString(),anyString(), anyString(), any(), anyString(), Mockito.any(TransferPromptpayValidateRequest.class))).thenThrow(FeignException.FeignClientException.class);
        try {
            Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.promptpayValidate(correlationId, crmId, deviceId, falsePreLogin, headers, mockPromptpayValidateRequest(requestAmt)));
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId,"Test");
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);
            Mockito.verify(commonTransferService, Mockito.times(1)).getCrmIdFromDeviceId(crmId,deviceId);
            Mockito.verify(v2TransferServiceClient, Mockito.times(1)).promptpayValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(deviceId), Mockito.eq(falsePreLogin), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferPromptpayValidateRequest.class));
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testPromptpayValidateAmountLimitFail() throws TMBCommonException{
        String requestAmt = "10000";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, "Test")).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(true);
        Mockito.when(commonTransferService.getCrmIdFromDeviceId(crmId,deviceId)).thenReturn(crmId);
        try {
            Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.promptpayValidate(correlationId, crmId, deviceId, falsePreLogin, new HttpHeaders(), mockPromptpayValidateRequest(requestAmt)));
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId,"Test");
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);
            Mockito.verify(commonTransferService, Mockito.times(1)).getCrmIdFromDeviceId(crmId,deviceId);

        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void testPromptpayValidateTransferFromViewOnly() throws TMBCommonException{
        String requestAmt = "10000";
        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, "Test")).thenReturn(mockDepositAccount(true));
        Mockito.when(commonTransferService.getCrmIdFromDeviceId(crmId,deviceId)).thenReturn(crmId);
        try {
            Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.promptpayValidate(correlationId, crmId, deviceId, falsePreLogin, headers, mockPromptpayValidateRequest(requestAmt)));
            Mockito.verify(commonTransferService, Mockito.times(1)).validateAccountTransfer(crmId, correlationId,"Test");
            Mockito.verify(commonTransferService, Mockito.times(1)).isOverAvailableBalance(avAmount, requestAmt);
            Mockito.verify(commonTransferService, Mockito.times(1)).getCrmIdFromDeviceId(crmId,deviceId);
            Mockito.verify(v2TransferServiceClient, Mockito.times(1)).promptpayValidate(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(deviceId), Mockito.eq(falsePreLogin), Mockito.eq(ipAddress), Mockito.eq(appVersion), Mockito.any(TransferPromptpayValidateRequest.class));
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    MbTransferOnUsValidateRequest mockMbTransferOnUsValidateRequest(String fromAccountNo){
        MbTransferOnUsValidateRequest mbTransferOnUsValidateRequest = new MbTransferOnUsValidateRequest();
        mbTransferOnUsValidateRequest.setFromAccountNo(fromAccountNo);
        mbTransferOnUsValidateRequest.setToAccountNo("**********");
        mbTransferOnUsValidateRequest.setBankCode("bank_code");
        mbTransferOnUsValidateRequest.setToFavoriteName("to_fav_name");
        mbTransferOnUsValidateRequest.setAmount("100");
        mbTransferOnUsValidateRequest.setCategoryId("category_id");
        mbTransferOnUsValidateRequest.setNote("note");
        mbTransferOnUsValidateRequest.setFlow("flow");
        mbTransferOnUsValidateRequest.setDepositNo("deposit_no");
        return mbTransferOnUsValidateRequest;
    }

    @SuppressWarnings("SameParameterValue")
    DepositAccount mockDepositAccount(BigDecimal availableBalance, boolean isHideAccount, String fromAccountNo){
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAvailableBalance(availableBalance);
        depositAccount.setHideAccountFlag(isHideAccount);
        depositAccount.setAccountNumber(fromAccountNo);
        return depositAccount;
    }

    private ResponseEntity<TmbServiceResponse<TransferOnUsValidateResponse>> mockTransferOnUsValidateResponse(){
        TransferOnUsValidateResponse transferOnUsValidateResponse = new TransferOnUsValidateResponse();
        transferOnUsValidateResponse.setTransId("test_transId");
        transferOnUsValidateResponse.setInterest("test_interest");
        transferOnUsValidateResponse.setPrincipal("test_principal");
        transferOnUsValidateResponse.setPenalty("test_penalty");
        transferOnUsValidateResponse.setNetAmount("test_netAmount");
        transferOnUsValidateResponse.setTax("test_tax");
        transferOnUsValidateResponse.setAmount("100");
        transferOnUsValidateResponse.setIsRequireConfirmPin(false);
        transferOnUsValidateResponse.setToAccountName("test_toAccountName");
        transferOnUsValidateResponse.setIsRequireFr(Boolean.FALSE);

        TmbServiceResponse<TransferOnUsValidateResponse> tmbOneServiceResponse = new TmbServiceResponse<>();
        tmbOneServiceResponse.setData(transferOnUsValidateResponse);
        tmbOneServiceResponse.setStatus(new Status(ResponseCode.SUCCESS.getCode(),ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService(), new Description("en", "th")));
        return new ResponseEntity<>(tmbOneServiceResponse, HttpStatus.OK);
    }

    private TransferOnUsConfirmResponse mockOnUsConfirmResponse(){
        TransferOnUsConfirmResponse transferOnUsConfirmResponse = new TransferOnUsConfirmResponse();
        transferOnUsConfirmResponse.setReferenceNo("test_reference_no");
        return transferOnUsConfirmResponse;
    }

    private MbTransferOnUsConfirmRequest mockMbTransferOnUsConfirmRequest(){
        MbTransferOnUsConfirmRequest mbTransferOnUsConfirmRequest = new MbTransferOnUsConfirmRequest();
        mbTransferOnUsConfirmRequest.setTransId("transId");
        return mbTransferOnUsConfirmRequest;
    }

    @Test
    void onUsValidateSuccessCase() throws TMBCommonException {
        String correlationId = "1234",  crmId = "1233", fromAccountNo = "********";
        when(commonTransferService.validateAccountTransfer(any(),any(),any(MbTransferOnUsValidateRequest.class))).thenReturn(mockDepositAccount(new BigDecimal(1000),false,fromAccountNo));
        when(commonTransferService.isOverAvailableBalance(any(),any())).thenReturn(false);
        when(v2TransferServiceClient.onUsValidate(any(), any(), any(), any(), any(), any())).thenReturn(mockTransferOnUsValidateResponse());

        TmbServiceResponse<TransferOnUsValidateResponse> result = v2TransferService.onUsValidate(correlationId, crmId, new HttpHeaders(), mockMbTransferOnUsValidateRequest(fromAccountNo));
        Assertions.assertEquals(Boolean.FALSE, result.getData().getIsRequireFr());
        verify(commonTransferService, times(1)).validateAccountTransfer(any(),any(),any(MbTransferOnUsValidateRequest.class));
        verify(commonTransferService, times(1)).isOverAvailableBalance(any(),any());
        verify(v2TransferServiceClient, times(1)).onUsValidate(any(),any(),any(),any(),any(),any());
        Assertions.assertEquals(Objects.requireNonNull(mockTransferOnUsValidateResponse().getBody()).getData().getAmount(),result.getData().getAmount());
        Assertions.assertEquals(Objects.requireNonNull(mockTransferOnUsValidateResponse().getBody()).getData().getToAccountName(),result.getData().getToAccountName());
    }

    @Test
    void onUsValidateWhenTransferExceptionCase() throws TMBCommonException {
        String correlationId = "1234",  crmId = "1233", fromAccountNo = "********";
        when(commonTransferService.validateAccountTransfer(any(),any(),any(MbTransferOnUsValidateRequest.class))).thenReturn(mockDepositAccount(new BigDecimal(1000),false,fromAccountNo));
        when(commonTransferService.isOverAvailableBalance(any(),any())).thenReturn(false);
        when(v2TransferServiceClient.onUsValidate(any(), any(),any(),any(),any(),any())).thenThrow(FeignException.FeignClientException.class);

        try {
            Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.onUsValidate(correlationId,crmId,new HttpHeaders(),mockMbTransferOnUsValidateRequest(fromAccountNo)));
            verify(commonTransferService, times(1)).validateAccountTransfer(any(),any(),any(MbTransferOnUsValidateRequest.class));
            verify(commonTransferService, times(1)).isOverAvailableBalance(any(),any());
            verify(v2TransferServiceClient, times(1)).onUsValidate(any(),any(),any(),any(),any(),any());
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void onUsValidateWhenRequestAmountIsOverBalanceCase() throws TMBCommonException {
        String correlationId = "1234",  crmId = "1233", fromAccountNo = "********";
        when(commonTransferService.validateAccountTransfer(any(),any(),any(MbTransferOnUsValidateRequest.class))).thenReturn(mockDepositAccount(new BigDecimal(1000),false,fromAccountNo));
        when(commonTransferService.isOverAvailableBalance(any(),any())).thenReturn(true);
        try {
            Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.onUsValidate(correlationId,crmId, new HttpHeaders(),mockMbTransferOnUsValidateRequest(fromAccountNo)));
            verify(commonTransferService, times(1)).validateAccountTransfer(any(),any(),any(MbTransferOnUsValidateRequest.class));
            verify(commonTransferService, times(1)).isOverAvailableBalance(any(),any());

        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void onUsValidateWhenTransferFromViewOnlyCase() throws TMBCommonException {
        String correlationId = "1234",  crmId = "1233", fromAccountNo = "********";
        when(commonTransferService.validateAccountTransfer(any(), any(), any(MbTransferOnUsValidateRequest.class))).thenThrow(TMBCommonException.class);
        try {
            Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.onUsValidate(correlationId,crmId,new HttpHeaders(),mockMbTransferOnUsValidateRequest(fromAccountNo)));
            verify(commonTransferService, times(1)).validateAccountTransfer(any(),any(),any(MbTransferOnUsValidateRequest.class));
        } catch (TMBCommonException e) {
            Assertions.fail();
        }
    }

    @Test
    void onUsValidateWhenTransferToTDIsInServiceHourCase() throws TMBCommonException {
        String correlationId = "1234",  crmId = "1233", fromAccountNo = "********";
        MbTransferOnUsValidateRequest request = new MbTransferOnUsValidateRequest();
        request.setToAccountNo("**********");
        request.setFromAccountNo("********99");
        when(commonTransferService.validateAccountTransfer(any(),any(),any(MbTransferOnUsValidateRequest.class))).thenReturn(mockDepositAccount(new BigDecimal(1000),false,fromAccountNo));
        when(commonTransferService.isOverAvailableBalance(any(),any())).thenReturn(false);
        when(v2TransferServiceClient.onUsValidate(any(), any(), any(), any(), any(), any())).thenReturn(mockTransferOnUsValidateResponse());
        when(transferServiceClient.getAccountConfiguration(anyString())).thenReturn(ResponseEntity.ok(mockGetTransferConfiguration()));
        when(commonTransferService.isNonWorkingHour(anyString(),anyString(), any())).thenReturn(false);

        Assertions.assertDoesNotThrow(()-> v2TransferService.onUsValidate(correlationId,crmId,new HttpHeaders(),request));
    }

    @Test
    void onUsValidateWhenTransferToTDIsNonServiceHourCase()  {
        String correlationId = "1234",  crmId = "1233";
        MbTransferOnUsValidateRequest request = new MbTransferOnUsValidateRequest();
        request.setToAccountNo("**********");
        when(transferServiceClient.getAccountConfiguration(anyString())).thenReturn(ResponseEntity.ok(mockGetTransferConfiguration()));
        when(commonTransferService.isNonWorkingHour(anyString(),anyString(), any())).thenReturn(true);
        Assertions.assertThrows(TMBCommonException.class, ()-> v2TransferService.onUsValidate(correlationId,crmId,new HttpHeaders(),request));
    }

    @Test
    void onUsValidateWhenTransferToTDCanNotGetConfigCase() throws TMBCommonException {
        String correlationId = "1234",  crmId = "1233", fromAccountNo = "********99";
        MbTransferOnUsValidateRequest request = new MbTransferOnUsValidateRequest();
        request.setToAccountNo("**********");

        TmbOneServiceResponse<TransferModuleModel> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(new TransferModuleModel());
        when(transferServiceClient.getAccountConfiguration(anyString())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
        when(commonTransferService.validateAccountTransfer(any(),any(),any(MbTransferOnUsValidateRequest.class))).thenReturn(mockDepositAccount(new BigDecimal(1000),false,fromAccountNo));
        when(commonTransferService.isOverAvailableBalance(any(),any())).thenReturn(false);
        when(v2TransferServiceClient.onUsValidate(any(), any(), any(), any(), any(), any())).thenReturn(mockTransferOnUsValidateResponse());

        Assertions.assertDoesNotThrow(()-> v2TransferService.onUsValidate(correlationId,crmId,new HttpHeaders(),request));
    }

    @Test
    void onUsConfirmSuccessCase() throws TMBCommonException {
        String correlationId = "1234",  crmId = "1233";
        when(v2TransferServiceClient.onUsConfirm(any(), any(), any(), any(), any(), any())).thenReturn(mockResponseEntityOk(mockOnUsConfirmResponse()));

        TmbServiceResponse<TransferOnUsConfirmResponse> result = v2TransferService.onUsConfirm(correlationId,crmId,headers, mockMbTransferOnUsConfirmRequest());

        verify(v2TransferServiceClient, times(1)).onUsConfirm(any(), any(), any(), any(), any(), any());
        Assertions.assertEquals(mockOnUsConfirmResponse().getReferenceNo(),result.getData().getReferenceNo());
        Assertions.assertEquals(ResponseCode.SUCCESS.getCode(),result.getStatus().getCode());
    }

    private ResponseEntity<TmbServiceResponse<TransferPromptpayConfirmResponse>>
    mockTransferPromptpayConfirm() {
        TransferPromptpayConfirmResponse res = new TransferPromptpayConfirmResponse();
        res.setReferenceNo("Test");
        res.setRemainingBalance("1000");
        res.setTransferCreatedDatetime("Test");
        res.setIsToOwnAccount(true);
        res.setQr("Test");

        return mockResponseEntityOk(res);
    }

    private MbTransferPromptpayConfirmRequest mockTransferPromptpayConfirmRequest(){
        MbTransferPromptpayConfirmRequest req = new MbTransferPromptpayConfirmRequest();
        req.setTransId("Test");
        req.setFrUuid("UUID_12345");

        return req;
    }

    @Test
    void testPromptpayConfirm() throws TMBCommonException {
        Mockito.when(commonTransferService.getCrmIdFromDeviceId(crmId,deviceId)).thenReturn(crmId);
        Mockito.when(
                        v2TransferServiceClient.promptpayConfirm(
                                anyString(),
                                anyString(),
                                anyString(),
                                anyString(),
                                anyString(),
                                anyString(),
                                anyString(),
                                Mockito.any(TransferPromptpayConfirmRequest.class)))
                .thenReturn(mockTransferPromptpayConfirm());

        v2TransferService.promptpayConfirm(correlationId, crmId, deviceId,timeStamp, headers, mockTransferPromptpayConfirmRequest());

        Mockito.verify(v2TransferServiceClient, Mockito.times(1)).promptpayConfirm(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(deviceId),Mockito.eq(appVersion)
                ,Mockito.eq(deviceModel),Mockito.eq(timeStamp), Mockito.eq(ipAddress),Mockito.any(TransferPromptpayConfirmRequest.class));
        Mockito.verify(commonTransferService, Mockito.times(1)).getCrmIdFromDeviceId(crmId, deviceId);
    }

    @Test
    void testPromptpayConfirmNull() throws TMBCommonException {
        Mockito.when(commonTransferService.getCrmIdFromDeviceId(crmId,deviceId)).thenReturn(crmId);
        Mockito.when(v2TransferServiceClient.promptpayConfirm(anyString(), anyString(),anyString(),anyString(),anyString(),anyString(),anyString(), Mockito.any(TransferPromptpayConfirmRequest.class))).thenReturn(null);
        Assertions.assertThrows(TMBCommonException.class, () -> v2TransferService.promptpayConfirm(correlationId, crmId, deviceId, timeStamp, headers, mockTransferPromptpayConfirmRequest()));
        Mockito.verify(v2TransferServiceClient, Mockito.times(1)).promptpayConfirm(Mockito.eq(correlationId), Mockito.eq(crmId), Mockito.eq(deviceId), Mockito.eq(appVersion)
                , Mockito.eq(deviceModel), Mockito.eq(timeStamp),  Mockito.eq(ipAddress), Mockito.any(TransferPromptpayConfirmRequest.class));
        Mockito.verify(commonTransferService, Mockito.times(1)).getCrmIdFromDeviceId(crmId, deviceId);
    }

    @ParameterizedTest
    @CsvSource({"ppgw_b247048","promptpay_linked"})
    void testPromptPayValidateWhenNumberNotRegister(String errorCode) throws TMBCommonException {
        String requestAmt = "10000";
        String thaiDescription = "thai description";
        String englishDescription = "english description";

        TmbServiceResponse<TransferPromptpayValidateResponse> tmbServiceResponse = new TmbServiceResponse<>();
        Status status = new Status();
        status.setCode(errorCode);
        status.setService("transfer-service");
        status.setMessage("message");
        Description description = new Description(englishDescription, thaiDescription);
        status.setDescription(description);
        tmbServiceResponse.setData(null);
        tmbServiceResponse.setStatus(status);

        Mockito.when(commonTransferService.validateAccountTransfer(crmId, correlationId, "Test")).thenReturn(mockDepositAccount(false));
        Mockito.when(commonTransferService.isOverAvailableBalance(avAmount, requestAmt)).thenReturn(false);
        Mockito.when(commonTransferService.getCrmIdFromDeviceId(crmId,deviceId)).thenReturn(crmId);
        Mockito.when(v2TransferServiceClient.promptpayValidate(anyString(),anyString(),anyString(),anyString(),any(),anyString(),Mockito.any(TransferPromptpayValidateRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonExceptionWithoutMappingPhrases exception = Assertions.assertThrows(TMBCommonExceptionWithoutMappingPhrases.class, () -> v2TransferService.promptpayValidate(correlationId, crmId, deviceId, falsePreLogin, headers, mockPromptpayValidateRequest(requestAmt)));
        Assertions.assertEquals(thaiDescription, exception.getDescription().getTh());
        Assertions.assertEquals(englishDescription, exception.getDescription().getEn());

    }

    @ParameterizedTest
    @CsvSource({
            TRANSFER_ERROR_TTB_CBS_PREFIX + "8004,1142-B-01-IC-03-01-007",
            TRANSFER_ERROR_TTB_CBS_PREFIX + "8005,1151-B-01-IC-01-01-000",
            TRANSFER_ERROR_TTB_CBS_PREFIX + "8009,1142-B-01-IC-03-01-007"
    })
    void testConfirmOnUsMapDsCode(String errorCode, String dsErrorCode) {

        String correlationId = "1234", crmId = "1233";
        TmbServiceResponse<TransferOnUsConfirmResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(new Status(errorCode, "", "", new Description()));
        ResponseEntity<TmbServiceResponse<TransferOnUsConfirmResponse>> response = ResponseEntity.ok(tmbServiceResponse);
        when(v2TransferServiceClient.onUsConfirm(any(), any(), any(), any(), any(), any()))
                .thenReturn(response);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> v2TransferService
                .onUsConfirm(correlationId, crmId, headers, mockMbTransferOnUsConfirmRequest()));

        verify(v2TransferServiceClient, times(1)).onUsConfirm(any(), any(), any(), any(), any(), any());
        Assertions.assertEquals(dsErrorCode, exception.getErrorCode());

    }

    private TmbOneServiceResponse<TransferModuleModel> mockGetTransferConfiguration(){
        TmbOneServiceResponse<TransferModuleModel> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        CommonTime commonTime = new CommonTime();
        commonTime.setStart("06:00");
        commonTime.setEnd("22:30");
        tmbOneServiceResponse.setData(TransferModuleModel.builder()
                .withdrawTdCutoffTime(commonTime).build());
        return tmbOneServiceResponse;
    }

}