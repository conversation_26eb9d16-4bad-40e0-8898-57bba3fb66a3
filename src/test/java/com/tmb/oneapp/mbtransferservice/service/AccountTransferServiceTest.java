package com.tmb.oneapp.mbtransferservice.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.mbtransferservice.client.CustomerAccountBizClient;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.AccountSaving;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferResponse;
import feign.FeignException;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_ACTIVE;
import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_DORMANT;
import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_INACTIVE;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.ACCOUNT_STATUS_CLOSE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AccountTransferServiceTest {

    @InjectMocks
    AccountTransferServiceImpl accountTransferService;
    @Mock
    CustomerAccountBizClient customerAccountBizClient;

    String crmId;
    String correlationId;
    HttpHeaders headers;
    List<DepositAccount> depositAccountsPassByReference;
    List<DepositAccount> accountBalanceResponse;
    @BeforeEach
    void setup() {
        crmId = "0001010203021301230";
        correlationId = "*****************";

        headers = new HttpHeaders();
        headers.add(CommonConstant.HEADER_X_CRM_ID, crmId);
        headers.add(CommonConstant.HEADER_X_CORRELATION_ID, correlationId);

        depositAccountsPassByReference = new ArrayList<>();
        accountBalanceResponse = new ArrayList<>();
    }

    @Test
    void testGetDepositAccountList_ShouldReturnDepositAccountList() throws TMBCommonException {
        mockGetAccountListReturnDepositAccountList();
        mockGetAccountBalanceReturnAccountBalanceActive();

        List<DepositAccountTransfer> actual = accountTransferService.getDepositAccountList(correlationId, crmId, false, true);

        assertEquals(7, actual.size());
    }

    @Test
    void testGetDepositAccountList_WhenFirstAccountHaveAvailableBalance_ShouldReturnFirstAccount() throws TMBCommonException {
        mockGetAccountListReturnFirstAccountHaveAvailableBalance();

        List<DepositAccountTransfer> actual = accountTransferService.getDepositAccountList(correlationId, crmId, false, true);

        DepositAccountTransfer actualFirstAccount = actual.get(0);
        assertNotNull(actualFirstAccount.getAvailableBalance());
        assertNotNull(actualFirstAccount.getAccountStatus());
        assertShouldNotCallGetBalance();
    }

    @Test
    void testGetDepositAccountList_WhenFirstAccountNotHaveBalance_ShouldReturnFirstAccountWithBalance() throws TMBCommonException {
        mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(depositAccountsPassByReference);
        mockGetAccountBalanceReturnAccountBalanceActive();

        List<DepositAccountTransfer> actual = accountTransferService.getDepositAccountList(correlationId, crmId, false, true);

        DepositAccountTransfer actualFirstAccount = actual.get(0);
        assertEquals(depositAccountsPassByReference.get(0).getAccountNumber(), actualFirstAccount.getAccountNumber());
        assertNotNull(actualFirstAccount.getAvailableBalance());
        assertNotNull(actualFirstAccount.getAccountStatus());
        assertNotNull(actualFirstAccount.getLinkedAccount());
        assertEquals("accountNameFromGetAvailableBalance", actualFirstAccount.getAccountName());
        verify(customerAccountBizClient, times(1)).getAccountBalance(any(), any(), anyList());
    }

    @Test
    void testGetDepositAccountList_WhenFirstAccountIsNotEligible_ShouldReturnEligibleAccountWithBalance() throws TMBCommonException {
        mockGetAccountListReturnLastAccountEligible(depositAccountsPassByReference);
        mockGetAccountBalanceReturnAccountBalanceActive();

        List<DepositAccountTransfer> actual = accountTransferService.getDepositAccountList(correlationId, crmId, false, true);

        DepositAccountTransfer actualEligibleAccountWithBalance = actual.get(0);
        DepositAccount expectEligibleAccount = depositAccountsPassByReference.get(6);
        assertEquals(expectEligibleAccount.getAccountNumber(), actualEligibleAccountWithBalance.getAccountNumber());
        assertNotNull(actualEligibleAccountWithBalance.getAvailableBalance());
        assertNotNull(actualEligibleAccountWithBalance.getAccountStatus());
        assertNotNull(actualEligibleAccountWithBalance.getLinkedAccount());
        verify(customerAccountBizClient, times(1)).getAccountBalance(any(), any(), anyList());
    }

    @Test
    void testGetDepositAccountListWhenCustomerAccountBizGotExceptionShouldThrowsTmbCommonException() {
        when(customerAccountBizClient.getAccountList(correlationId, crmId, false, true)).thenThrow(FeignException.FeignClientException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> accountTransferService.getDepositAccountList(correlationId, crmId, false, true));

        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }

    @Test
    void testGetDepositAccountListWhenDataIsEmptyShouldThrowsTmbCommonException() {
        mockGetAccountListReturnEmptyList();

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> accountTransferService.getDepositAccountList(correlationId, crmId, false, true));

        assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(), exception.getErrorCode());
    }

    @Test
    void testGetDepositAccountList_WhenGetAccountBalanceGotFeignException_ShouldThrowsException() {
        mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(depositAccountsPassByReference);
        mockGetAccountBalanceReturnThrowFeignException();

        assertThrows(TMBCommonException.class, () -> accountTransferService.getDepositAccountList(correlationId, crmId, false, true));
    }

    @Test
    void testGetDepositAccountList_WhenGetAccountBalanceAreActiveOrInactive_ShouldReturnAvailableBalanceAndLinkedAccount() throws TMBCommonException {
        mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(depositAccountsPassByReference);
        mockGetAccountBalanceReturnAccountBalanceActive();

        List<DepositAccountTransfer> actual = accountTransferService.getDepositAccountList(correlationId, crmId, false, true);

        DepositAccountTransfer actualAccountGetBalance = actual.get(0);
        DepositAccount expectAccountBalance = accountBalanceResponse.get(0);
        assertEquals(expectAccountBalance.getLinkedAccount(), actualAccountGetBalance.getLinkedAccount());
        assertEquals(expectAccountBalance.getAvailableBalance(), actualAccountGetBalance.getAvailableBalance());
        assertEquals(expectAccountBalance.getAccountStatus(), actualAccountGetBalance.getAccountStatus());
    }

    @Test
    void testGetDepositAccountList_WhenGetAccountBalanceIsCloseAccount_ShouldRemoveTheAccountFromResponse() throws TMBCommonException {
        int expectAccountSize;
        mockGetAccountListReturnFirstAccountWillCallGetBalance();
        mockGetAccountBalanceReturnCloseAccount();

        List<DepositAccountTransfer> actual = accountTransferService.getDepositAccountList(correlationId, crmId, false, true);

        expectAccountSize = depositAccountsPassByReference.size() - 1;
        assertEquals(expectAccountSize, actual.size());
//        TODO old code still not work
    }

    @Test
    void testGetDepositAccountList_WhenHaveOnlyCloseAccount_ShouldThrowTMBCommonException() {
        DepositAccount accountShouldCallGetBalance = new DepositAccount().setAvailableBalance(null).setAccountNumber("**********").setAccountStatus(ACCOUNT_STATUS_ACTIVE).setTransferOwnTTBMapCode("010");
        depositAccountsPassByReference.add(accountShouldCallGetBalance);
        TmbOneServiceResponse<AccountSaving> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        tmbOneServiceResponse.setData(new AccountSaving().setDepositAccountLists(depositAccountsPassByReference));
        when(customerAccountBizClient.getAccountList(correlationId, crmId, false, true)).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));

        mockGetAccountBalanceReturnCloseAccount();

        assertThrows(TMBCommonException.class, () -> accountTransferService.getDepositAccountList(correlationId, crmId, false, true));
//        TODO old code still not work
    }


    @Test
    void testGetDepositAccountList_WhenGetAccountBalanceIsDormantAccount_ShouldReturnStatusDormant() throws TMBCommonException {
        int expectAccountSize;
        mockGetAccountListReturnFirstAccountWillCallGetBalance();
        mockGetAccountBalanceReturnDormantAccount();

        List<DepositAccountTransfer> actual = accountTransferService.getDepositAccountList(correlationId, crmId, false, true);

        expectAccountSize = depositAccountsPassByReference.size();
        assertEquals(expectAccountSize, actual.size());
        assertEquals(ACCOUNT_STATUS_DORMANT, actual.get(0).getAccountStatus());
    }

    @Test
    void testGetDepositAccountList_WhenGetAccountBalanceIsOtherAccount_ShouldDoesNotThrows() {
        mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(depositAccountsPassByReference);
        mockGetAccountBalanceReturnOtherStatusAccount();

        assertDoesNotThrow(() -> accountTransferService.getDepositAccountList(correlationId, crmId, false, true));
    }

    @Test
    void testGetAccountBalance_ShouldSuccess() throws TMBCommonException {
        List<DepositAccount> depositAccountList = new ArrayList<>();
        depositAccountList.add(new DepositAccount().setAccountNumber("accountNumber").setAccountStatus("accountStatus").setAvailableBalance(new BigDecimal("100.50")).setLinkedAccount("linkedAccount"));

        TmbOneServiceResponse<List<DepositAccount>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(depositAccountList);
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), any())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));

        AccountBalanceTransferResponse actual = accountTransferService.getAccountBalance("**********", headers);

        assertEquals("accountNumber", actual.getAccountNumber());
        assertEquals("accountStatus", actual.getAccountStatus());
        assertEquals("linkedAccount", actual.getLinkedAccount());
        assertEquals(new BigDecimal("100.50"), actual.getAvailableBalance());
    }

    @Test
    void testGetAccountBalance_WhenGetAccountBalanceReturnEmptyList_ShouldReturnCloseAccount() throws TMBCommonException {
        TmbOneServiceResponse<List<DepositAccount>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(Collections.emptyList());
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), any())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));

        AccountBalanceTransferResponse actual = accountTransferService.getAccountBalance("**********", headers);

        assertEquals(ACCOUNT_STATUS_CLOSE, actual.getAccountStatus());
        assertEquals("**********", actual.getAccountNumber());
        assertEquals(new BigDecimal("0.00"), actual.getAvailableBalance());
        assertNull(actual.getLinkedAccount());
    }

    @Test
    void testGetAccountBalance_WhenValidateRequestFailed_ShouldThrowTMBCommonException() {
        assertThrows(TMBCommonException.class, () -> accountTransferService.getAccountBalance("invalidLengthAccount", headers));
        assertThrows(TMBCommonException.class, () -> accountTransferService.getAccountBalance("length", headers));
        assertThrows(TMBCommonException.class, () -> accountTransferService.getAccountBalance(null, headers));

        HttpHeaders headersWithOutValue = new HttpHeaders();
        assertThrows(TMBCommonException.class, () -> accountTransferService.getAccountBalance("**********",
                headersWithOutValue));

        HttpHeaders headersWithOutCrmId = new HttpHeaders();
        headersWithOutCrmId.add(CommonConstant.HEADER_X_CORRELATION_ID, correlationId);
        assertThrows(TMBCommonException.class, () -> accountTransferService.getAccountBalance("**********",
                headersWithOutCrmId));

        HttpHeaders headersWithOutCorrelationId = new HttpHeaders();
        headersWithOutCorrelationId.add(CommonConstant.HEADER_X_CRM_ID, crmId);
        assertThrows(TMBCommonException.class, () -> accountTransferService.getAccountBalance("**********",
                headersWithOutCorrelationId));
    }

    @Test
    void testGetAccountBalance_WhenGetAccountBalanceFeignException_ShouldThrowTMBCommonException() {
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), any())).thenThrow(FeignException.class);

        assertThrows(TMBCommonException.class, () -> accountTransferService.getAccountBalance("**********", headers));
    }

    @ParameterizedTest
    @CsvSource({
            "10.00, ",
            " , DORMANT",
            " , "

    })
    void testGetAccountBalance_WhenGetAccountBalanceReturnMandatoryFieldIsNull_ShouldThrowNullPointerException(String availableBalanceString, String accountStatus) {
        BigDecimal availableBalance = availableBalanceString != null ? new BigDecimal(availableBalanceString) : null;

        List<DepositAccount> depositAccountList = new ArrayList<>();
        depositAccountList.add(new DepositAccount().setAccountStatus(accountStatus).setAvailableBalance(availableBalance));

        TmbOneServiceResponse<List<DepositAccount>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(depositAccountList);
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), any())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));

        assertThrows(NullPointerException.class, () -> accountTransferService.getAccountBalance("**********", headers));
    }

    @ParameterizedTest
    @CsvSource({
            "DORMANT, true, 10.00, false",
            "DORMANT, false, 10.00, false",
            "DORMANT, true, , false",
            "DORMANT, false, , false",
            "ACTIVE, true, 10.00, false",
            "ACTIVE, true, , false",
            "ACTIVE, false, 10.00, false",

            "ACTIVE, false, , true",

    })
    void isShouldGetBalance(String accountStatus, boolean isHideAccount, String availableBalanceString, boolean expected) {
        BigDecimal availableBalance = availableBalanceString != null ? new BigDecimal(availableBalanceString) : null;

        assertEquals(expected, accountTransferService.isShouldGetBalance(accountStatus, isHideAccount, availableBalance));
    }

    private void mockGetAccountListReturnDepositAccountList() {
        List<DepositAccount> eligibleAccountShouldReturnToFE = new ArrayList<>();
        List<DepositAccount> dormantOrHideShouldReturnToFE = new ArrayList<>();
        List<DepositAccount> notReturnToFE = new ArrayList<>();

        String showAccount = "01";
        // For account return to FE
        eligibleAccountShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, false, showAccount));
        eligibleAccountShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, false, showAccount));

        dormantOrHideShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, true, showAccount));
        dormantOrHideShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, true, showAccount));
        dormantOrHideShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, true, showAccount));
        dormantOrHideShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, false, showAccount));

        // For account not return to FE
        String deleteStatus = "02";
        String closeStatus = "05";
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, false, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, false, closeStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, true, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, true, closeStatus));

        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, false, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, false, closeStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, true, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, true, closeStatus));

        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, true, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, true, closeStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, false, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, false, closeStatus));

        notReturnToFE.add(initDeposit("Other", true, deleteStatus));
        notReturnToFE.add(initDeposit("Other", true, closeStatus));
        notReturnToFE.add(initDeposit("Other", true, showAccount));
        notReturnToFE.add(initDeposit("Other", false, deleteStatus));
        notReturnToFE.add(initDeposit("Other", false, closeStatus));
        notReturnToFE.add(initDeposit("Other", false, showAccount));

        depositAccountsPassByReference.addAll(eligibleAccountShouldReturnToFE);
        depositAccountsPassByReference.addAll(dormantOrHideShouldReturnToFE);
        depositAccountsPassByReference.addAll(notReturnToFE);

        TmbOneServiceResponse<AccountSaving> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        tmbOneServiceResponse.setData(new AccountSaving().setDepositAccountLists(depositAccountsPassByReference));
        when(customerAccountBizClient.getAccountList(correlationId, crmId, false, true)).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private DepositAccount initDeposit(String accountStatus, boolean isHideAccount, String displayStatusAccount) {
        return new DepositAccount()
                .setAccountStatus(accountStatus)
                .setHideAccountFlag(isHideAccount)
                .setDisplayAccountStatus(displayStatusAccount)
                .setAccountNumber(RandomStringUtils.randomNumeric(10));
    }

    private void mockGetAccountListReturnFirstAccountHaveAvailableBalance() {
        BigDecimal availableBalance = new BigDecimal("100.50");
        List<DepositAccount> showAccount = new ArrayList<>();
        showAccount.add(new DepositAccount()
                .setAccountNumber("**********")
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setDisplayAccountStatus("01")
                .setTransferOwnTTBMapCode("010")
                .setAvailableBalance(availableBalance));

        List<DepositAccount> depositAccounts = new ArrayList<>(showAccount);

        TmbOneServiceResponse<AccountSaving> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        tmbOneServiceResponse.setData(new AccountSaving().setDepositAccountLists(depositAccounts));
        when(customerAccountBizClient.getAccountList(correlationId, crmId, false, true)).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(List<DepositAccount> depositAccounts) {
        BigDecimal availableBalanceNull = null;
        DepositAccount firstAccountNotHaveBalance = new DepositAccount().setAvailableBalance(availableBalanceNull).setAccountNumber("**********").setAccountStatus(ACCOUNT_STATUS_ACTIVE).setTransferOwnTTBMapCode("010");

        BigDecimal availableBalance = new BigDecimal("100.50");
        DepositAccount accountHaveBalance = new DepositAccount().setAvailableBalance(availableBalance).setAccountNumber("**********").setAccountStatus(ACCOUNT_STATUS_ACTIVE).setTransferOwnTTBMapCode("010");

        depositAccounts.add(firstAccountNotHaveBalance);
        depositAccounts.add(accountHaveBalance);

        TmbOneServiceResponse<AccountSaving> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        tmbOneServiceResponse.setData(new AccountSaving().setDepositAccountLists(depositAccounts));
        when(customerAccountBizClient.getAccountList(correlationId, crmId, false, true)).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetAccountListReturnLastAccountEligible(List<DepositAccount> depositAccounts) {
        String accountStatusDormant = ACCOUNT_STATUS_DORMANT;
        BigDecimal availableBalanceNull = null;
        boolean isHideAccount = true;

        BigDecimal availableBalanceHaveValue = new BigDecimal("10.00");

        List<DepositAccount> noEligibleAccounts = new ArrayList<>();
        noEligibleAccounts.add(new DepositAccount().setAvailableBalance(availableBalanceHaveValue).setAccountStatus(accountStatusDormant).setHideAccountFlag(false)        .setAccountNumber("**********"));
        noEligibleAccounts.add(new DepositAccount().setAvailableBalance(availableBalanceHaveValue).setAccountStatus(accountStatusDormant).setHideAccountFlag(isHideAccount).setAccountNumber("**********"));
        noEligibleAccounts.add(new DepositAccount().setAvailableBalance(availableBalanceHaveValue).setAccountStatus(ACCOUNT_STATUS_ACTIVE).setHideAccountFlag(isHideAccount).setAccountNumber("**********"));

        noEligibleAccounts.add(new DepositAccount().setAvailableBalance(availableBalanceNull)     .setAccountStatus(accountStatusDormant).setHideAccountFlag(false)        .setAccountNumber("**********"));
        noEligibleAccounts.add(new DepositAccount().setAvailableBalance(availableBalanceNull)     .setAccountStatus(accountStatusDormant).setHideAccountFlag(isHideAccount).setAccountNumber("**********"));
        noEligibleAccounts.add(new DepositAccount().setAvailableBalance(availableBalanceNull)     .setAccountStatus(ACCOUNT_STATUS_INACTIVE).setHideAccountFlag(isHideAccount).setAccountNumber("**********"));

        DepositAccount eligibleAccountWillShowFirstAccount = new DepositAccount()
                .setAvailableBalance(availableBalanceNull)
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setHideAccountFlag(false)
                .setAccountNumber("**********");

        depositAccounts.addAll(noEligibleAccounts);
        depositAccounts.add(eligibleAccountWillShowFirstAccount);

        TmbOneServiceResponse<AccountSaving> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        tmbOneServiceResponse.setData(new AccountSaving().setDepositAccountLists(depositAccounts));
        when(customerAccountBizClient.getAccountList(correlationId, crmId, false, true)).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetAccountListReturnFirstAccountWillCallGetBalance() {
        BigDecimal availableBalanceNull = null;
        DepositAccount firstAccountShouldCallGetBalance = new DepositAccount().setAvailableBalance(availableBalanceNull).setAccountNumber("**********").setAccountStatus(ACCOUNT_STATUS_ACTIVE).setTransferOwnTTBMapCode("010");

        DepositAccount accountHaveBalanceShouldNotCallGetBalance = new DepositAccount().setAvailableBalance(new BigDecimal("100.50")).setAccountNumber("**********").setAccountStatus(ACCOUNT_STATUS_ACTIVE).setTransferOwnTTBMapCode("010");


        depositAccountsPassByReference.add(firstAccountShouldCallGetBalance);
        depositAccountsPassByReference.add(accountHaveBalanceShouldNotCallGetBalance);

        TmbOneServiceResponse<AccountSaving> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        tmbOneServiceResponse.setData(new AccountSaving().setDepositAccountLists(depositAccountsPassByReference));
        when(customerAccountBizClient.getAccountList(correlationId, crmId, false, true)).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetAccountListReturnEmptyList() {
        AccountSaving emptyList = new AccountSaving();
        emptyList.setDepositAccountLists(Collections.emptyList());

        TmbOneServiceResponse<AccountSaving> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        TmbStatus tmbStatus = new TmbStatus(ResponseCode.SUCCESS.getCode(), ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService());
        tmbOneServiceResponse.setStatus(tmbStatus);
        tmbOneServiceResponse.setData(emptyList);
        when(customerAccountBizClient.getAccountList(correlationId, crmId, false, true)).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetAccountBalanceReturnAccountBalanceActive() {
        DepositAccount depositAccount = new DepositAccount()
                .setAvailableBalance(new BigDecimal("123.50"))
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setLinkedAccount("**********")
                .setAccountName("accountNameFromGetAvailableBalance");

        accountBalanceResponse.add(depositAccount);
        TmbOneServiceResponse<List<DepositAccount>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(accountBalanceResponse);
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), anyList())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetAccountBalanceReturnOtherStatusAccount() {
        String otherStatus = "OTHER";
        DepositAccount depositAccount = new DepositAccount()
                .setAvailableBalance(new BigDecimal("123.50"))
                .setAccountStatus(otherStatus)
                .setLinkedAccount("**********");

        accountBalanceResponse.add(depositAccount);
        TmbOneServiceResponse<List<DepositAccount>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(accountBalanceResponse);
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), anyList())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetAccountBalanceReturnDormantAccount() {
        String dormantAccount = ACCOUNT_STATUS_DORMANT;

        DepositAccount depositAccount = new DepositAccount()
                .setAvailableBalance(new BigDecimal("123.50"))
                .setAccountStatus(dormantAccount)
                .setLinkedAccount("**********");

        accountBalanceResponse.add(depositAccount);
        TmbOneServiceResponse<List<DepositAccount>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(accountBalanceResponse);
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), anyList())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetAccountBalanceReturnCloseAccount() {
        String closeAccount = ACCOUNT_STATUS_CLOSE;

        DepositAccount depositAccount = new DepositAccount()
                .setAvailableBalance(new BigDecimal("123.50"))
                .setAccountStatus(closeAccount)
                .setLinkedAccount("**********");

        accountBalanceResponse.add(depositAccount);
        TmbOneServiceResponse<List<DepositAccount>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(accountBalanceResponse);
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), anyList())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetAccountBalanceReturnThrowFeignException() {
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), anyList())).thenThrow(FeignException.class);
    }

    private void assertShouldNotCallGetBalance() {
        verify(customerAccountBizClient, times(0)).getAccountBalance(any(), any(), anyList());
    }
}
