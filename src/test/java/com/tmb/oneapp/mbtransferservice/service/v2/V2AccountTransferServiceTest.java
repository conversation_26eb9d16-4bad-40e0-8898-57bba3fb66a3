package com.tmb.oneapp.mbtransferservice.service.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.mbtransferservice.client.TransferServiceClient;
import com.tmb.oneapp.mbtransferservice.constant.CommonConstant;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.AccountSaving;
import com.tmb.oneapp.mbtransferservice.model.DepositAccount;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.TransferModuleModel;
import com.tmb.oneapp.mbtransferservice.model.V2DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.service.AccountTransferServiceImpl;
import com.tmb.oneapp.mbtransferservice.utils.TransferServiceUtils;
import feign.FeignException;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_ACTIVE;
import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_DORMANT;
import static com.tmb.oneapp.mbtransferservice.constant.AccountConstant.ACCOUNT_STATUS_INACTIVE;
import static com.tmb.oneapp.mbtransferservice.constant.CommonConstant.ACCOUNT_STATUS_CLOSE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class V2AccountTransferServiceTest {

    @InjectMocks
    V2AccountTransferServiceImpl service;
    @Mock
    AccountTransferServiceImpl accountTransferService;
    @Mock
    TransferServiceClient transferServiceClient;

    String crmId;
    String correlationId;
    HttpHeaders headers;
    List<DepositAccount> depositAccountsPassByReference;
    List<DepositAccount> accountBalanceResponse;

    @BeforeEach
    void setup() {
        crmId = "0001010203021301230";
        correlationId = "*****************";

        headers = new HttpHeaders();
        headers.add(CommonConstant.HEADER_X_CRM_ID, crmId);
        headers.add(CommonConstant.HEADER_X_CORRELATION_ID, correlationId);

        depositAccountsPassByReference = new ArrayList<>();
        accountBalanceResponse = new ArrayList<>();
    }

    @Test
    void testGetDepositAccountListShouldReturnDepositAccountList() throws TMBCommonException {
        mockGetAccountListReturnDepositAccountList();
        mockGetAccountBalanceReturnAccountBalanceActive();

        V2DepositAccountTransfer actual = service
                .getDepositAccountList(correlationId, crmId, false, true, null);

        assertEquals(7, actual.getAccountList().size());
        assertEquals(1, actual.getFcdAccountList().size());
    }

    @Test
    void testGetDepositAccountListWhenFirstAccountHaveAvailableBalanceShouldReturnFirstAccount()
            throws TMBCommonException {
        mockGetAccountListReturnFirstAccountHaveAvailableBalance();

        V2DepositAccountTransfer actual = service
                .getDepositAccountList(correlationId, crmId, false, true, null);

        DepositAccountTransfer actualFirstAccount = actual.getAccountList().get(0);
        assertNotNull(actualFirstAccount.getAvailableBalance());
        assertNotNull(actualFirstAccount.getAccountStatus());
        assertShouldNotCallGetBalance();
    }

    @Test
    void testGetDepositAccountListWhenFirstAccountNotHaveBalanceShouldReturnFirstAccountWithBalance()
            throws TMBCommonException {
        mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(depositAccountsPassByReference);
        mockGetAccountBalanceReturnAccountBalanceActive();

        V2DepositAccountTransfer actual = service
                .getDepositAccountList(correlationId, crmId, false, true, null);

        DepositAccountTransfer actualFirstAccount = actual.getAccountList().get(0);
        assertEquals(depositAccountsPassByReference.get(0).getAccountNumber(), actualFirstAccount.getAccountNumber());
        assertNotNull(actualFirstAccount.getAvailableBalance());
        assertNotNull(actualFirstAccount.getAccountStatus());
        assertNotNull(actualFirstAccount.getLinkedAccount());
        assertEquals("accountNameFromGetAvailableBalance", actualFirstAccount.getAccountName());
        verify(accountTransferService, times(1))
                .fetchAccountBalance(anyString(), anyString(), any(), anyString(), anyString());
    }

    @Test
    void testGetDepositAccountListWhenFirstAccountIsNotEligibleShouldReturnEligibleAccountWithBalance()
            throws TMBCommonException {
        mockGetAccountListReturnLastAccountEligible(depositAccountsPassByReference);
        mockGetAccountBalanceReturnAccountBalanceActive();

        V2DepositAccountTransfer actual = service
                .getDepositAccountList(correlationId, crmId, false, true, null);

        DepositAccountTransfer actualEligibleAccountWithBalance = actual.getAccountList().get(0);
        DepositAccount expectEligibleAccount = depositAccountsPassByReference.get(6);
        assertEquals(expectEligibleAccount.getAccountNumber(), actualEligibleAccountWithBalance.getAccountNumber());
        assertNotNull(actualEligibleAccountWithBalance.getAvailableBalance());
        assertNotNull(actualEligibleAccountWithBalance.getAccountStatus());
        assertNotNull(actualEligibleAccountWithBalance.getLinkedAccount());
        verify(accountTransferService, times(1))
                .fetchAccountBalance(anyString(), anyString(), any(), anyString(), anyString());
    }

    @Test
    void testGetDepositAccountListWhenCustomerAccountBizGotExceptionShouldThrowsTmbCommonException() throws TMBCommonException {
        when(accountTransferService.getAccountList(correlationId, crmId, false, true))
                .thenThrow(TransferServiceUtils.failException(ResponseCode.FAILED));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> service
                .getDepositAccountList(correlationId, crmId, false, true, null));

        assertEquals(ResponseCode.FAILED.getCode(), exception.getErrorCode());
    }

    @Test
    void testGetDepositAccountListWhenDataIsEmptyShouldThrowsTmbCommonException() throws TMBCommonException {
        mockGetAccountListReturnEmptyList();

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> service
                .getDepositAccountList(correlationId, crmId, false, true, null));

        assertEquals(ResponseCode.NO_ELIGIBLE_ACCOUNT.getCode(), exception.getErrorCode());
    }

    @Test
    void testGetDepositAccountListWhenGetAccountBalanceGotFeignExceptionShouldThrowsException() throws TMBCommonException {
        mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(depositAccountsPassByReference);
        mockGetAccountBalanceReturnThrowFeignException();

        assertThrows(TMBCommonException.class, () -> service
                .getDepositAccountList(correlationId, crmId, false, true, null));
    }

    @Test
    void testGetDepositAccountListWhenGetAccountBalanceAreActiveOrInactiveShouldReturnAvailableBalanceAndLinkedAccount()
            throws TMBCommonException {
        mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(depositAccountsPassByReference);
        mockGetAccountBalanceReturnAccountBalanceActive();

        V2DepositAccountTransfer actual = service
                .getDepositAccountList(correlationId, crmId, false, true, null);

        DepositAccountTransfer actualAccountGetBalance = actual.getAccountList().get(0);
        DepositAccount expectAccountBalance = accountBalanceResponse.get(0);
        assertEquals(expectAccountBalance.getLinkedAccount(), actualAccountGetBalance.getLinkedAccount());
        assertEquals(expectAccountBalance.getAvailableBalance(), actualAccountGetBalance.getAvailableBalance());
        assertEquals(expectAccountBalance.getAccountStatus(), actualAccountGetBalance.getAccountStatus());
    }

    @Test
    void testGetDepositAccountListWhenGetAccountBalanceIsCloseAccountShouldRemoveTheAccountFromResponse()
            throws TMBCommonException {
        int expectAccountSize;
        mockGetAccountListReturnFirstAccountWillCallGetBalance();
        mockGetAccountBalanceReturnCloseAccount();

        V2DepositAccountTransfer actual = service
                .getDepositAccountList(correlationId, crmId, false, true, null);

        expectAccountSize = depositAccountsPassByReference.size() - 1;
        assertEquals(expectAccountSize, actual.getAccountList().size());
    }

    @Test
    void testGetDepositAccountListWhenHaveOnlyCloseAccountShouldThrowTMBCommonException() throws TMBCommonException {
        DepositAccount accountShouldCallGetBalance = new DepositAccount()
                .setAvailableBalance(null)
                .setAccountNumber("**********")
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setTransferOwnTTBMapCode("010");
        depositAccountsPassByReference.add(accountShouldCallGetBalance);

        when(accountTransferService.getAccountList(correlationId, crmId, false, true))
                .thenReturn(new AccountSaving().setDepositAccountLists(depositAccountsPassByReference));

        mockGetAccountBalanceReturnCloseAccount();

        assertThrows(TMBCommonException.class, () -> service
                .getDepositAccountList(correlationId, crmId, false, true, null));
    }


    @Test
    void testGetDepositAccountListWhenGetAccountBalanceIsDormantAccountShouldReturnStatusDormant()
            throws TMBCommonException {
        int expectAccountSize;
        mockGetAccountListReturnFirstAccountWillCallGetBalance();
        mockGetAccountBalanceReturnDormantAccount();

        V2DepositAccountTransfer actual = service
                .getDepositAccountList(correlationId, crmId, false, true, null);

        expectAccountSize = depositAccountsPassByReference.size();
        assertEquals(expectAccountSize, actual.getAccountList().size());
        assertEquals(ACCOUNT_STATUS_DORMANT, actual.getAccountList().get(0).getAccountStatus());
    }

    @Test
    void testGetDepositAccountListWhenGetAccountBalanceIsOtherAccountShouldDoesNotThrows() throws TMBCommonException {
        mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(depositAccountsPassByReference);
        mockGetAccountBalanceReturnOtherStatusAccount();

        assertDoesNotThrow(() -> service
                .getDepositAccountList(correlationId, crmId, false, true, null));
    }

    private void mockGetAccountListReturnDepositAccountList() throws TMBCommonException {
        List<DepositAccount> eligibleAccountShouldReturnToFE = new ArrayList<>();
        List<DepositAccount> dormantOrHideShouldReturnToFE = new ArrayList<>();
        List<DepositAccount> notReturnToFE = new ArrayList<>();
        List<DepositAccount> fcdList = new ArrayList<>();

        String showAccount = "01";
        // For account return to FE
        eligibleAccountShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, false, showAccount));
        eligibleAccountShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, false, showAccount));

        dormantOrHideShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, true, showAccount));
        dormantOrHideShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, true, showAccount));
        dormantOrHideShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, true, showAccount));
        dormantOrHideShouldReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, false, showAccount));

        // For account not return to FE
        String deleteStatus = "02";
        String closeStatus = "05";
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, false, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, false, closeStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, true, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_ACTIVE, true, closeStatus));

        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, false, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, false, closeStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, true, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_INACTIVE, true, closeStatus));

        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, true, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, true, closeStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, false, deleteStatus));
        notReturnToFE.add(initDeposit(ACCOUNT_STATUS_DORMANT, false, closeStatus));

        notReturnToFE.add(initDeposit("Other", true, deleteStatus));
        notReturnToFE.add(initDeposit("Other", true, closeStatus));
        notReturnToFE.add(initDeposit("Other", true, showAccount));
        notReturnToFE.add(initDeposit("Other", false, deleteStatus));
        notReturnToFE.add(initDeposit("Other", false, closeStatus));
        notReturnToFE.add(initDeposit("Other", false, showAccount));

        DepositAccount depositAccount = initDeposit(ACCOUNT_STATUS_ACTIVE, false, showAccount);
        depositAccount.setTransferOwnTTBMapCode("111");
        depositAccount.setTransferOtherTTBMapCode("110");
        depositAccount.setFinancialId("**********");
        fcdList.add(depositAccount);

        depositAccountsPassByReference.addAll(eligibleAccountShouldReturnToFE);
        depositAccountsPassByReference.addAll(dormantOrHideShouldReturnToFE);
        depositAccountsPassByReference.addAll(notReturnToFE);

        when(accountTransferService.getAccountList(correlationId, crmId, false, true))
                .thenReturn(new AccountSaving()
                        .setDepositAccountLists(depositAccountsPassByReference)
                        .setFcdAccountLists(fcdList));
    }

    private DepositAccount initDeposit(String accountStatus, boolean isHideAccount, String displayStatusAccount) {
        return new DepositAccount()
                .setAccountStatus(accountStatus)
                .setHideAccountFlag(isHideAccount)
                .setDisplayAccountStatus(displayStatusAccount)
                .setAccountNumber(RandomStringUtils.randomNumeric(10));
    }

    private void mockGetAccountListReturnFirstAccountHaveAvailableBalance() throws TMBCommonException {
        BigDecimal availableBalance = new BigDecimal("100.50");
        List<DepositAccount> showAccount = new ArrayList<>();
        showAccount.add(new DepositAccount()
                .setAccountNumber("**********")
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setDisplayAccountStatus("01")
                .setTransferOwnTTBMapCode("010")
                .setAvailableBalance(availableBalance));

        List<DepositAccount> depositAccounts = new ArrayList<>(showAccount);

        when(accountTransferService.getAccountList(correlationId, crmId, false, true))
                .thenReturn(new AccountSaving().setDepositAccountLists(depositAccounts));
    }

    private void mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(List<DepositAccount> depositAccounts) throws TMBCommonException {
        DepositAccount firstAccountNotHaveBalance = new DepositAccount()
                .setAvailableBalance(null)
                .setAccountNumber("**********")
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setTransferOwnTTBMapCode("010");

        BigDecimal availableBalance = new BigDecimal("100.50");
        DepositAccount accountHaveBalance = new DepositAccount()
                .setAvailableBalance(availableBalance)
                .setAccountNumber("**********")
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setTransferOwnTTBMapCode("010");

        depositAccounts.add(firstAccountNotHaveBalance);
        depositAccounts.add(accountHaveBalance);

        when(accountTransferService.getAccountList(correlationId, crmId, false, true))
                .thenReturn(new AccountSaving()
                        .setDepositAccountLists(depositAccounts)
                        .setFcdAccountLists(List.of(new DepositAccount()
                                .setAccountNumber("**********")
                                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                                .setAccountType("CDA"))));
    }

    private void mockGetAccountListReturnLastAccountEligible(List<DepositAccount> depositAccounts) throws TMBCommonException {
        String accountStatusDormant = ACCOUNT_STATUS_DORMANT;
        boolean isHideAccount = true;

        BigDecimal availableBalanceHaveValue = new BigDecimal("10.00");

        List<DepositAccount> noEligibleAccounts = new ArrayList<>();
        noEligibleAccounts.add(new DepositAccount()
                .setAvailableBalance(availableBalanceHaveValue)
                .setAccountStatus(accountStatusDormant)
                .setHideAccountFlag(false)
                .setAccountNumber("**********"));
        noEligibleAccounts.add(new DepositAccount()
                .setAvailableBalance(availableBalanceHaveValue)
                .setAccountStatus(accountStatusDormant)
                .setHideAccountFlag(isHideAccount)
                .setAccountNumber("**********"));
        noEligibleAccounts.add(new DepositAccount()
                .setAvailableBalance(availableBalanceHaveValue)
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setHideAccountFlag(isHideAccount)
                .setAccountNumber("**********"));

        noEligibleAccounts.add(new DepositAccount()
                .setAvailableBalance(null)
                .setAccountStatus(accountStatusDormant)
                .setHideAccountFlag(false)
                .setAccountNumber("**********"));
        noEligibleAccounts.add(new DepositAccount()
                .setAvailableBalance(null)
                .setAccountStatus(accountStatusDormant)
                .setHideAccountFlag(isHideAccount)
                .setAccountNumber("**********"));
        noEligibleAccounts.add(new DepositAccount()
                .setAvailableBalance(null)
                .setAccountStatus(ACCOUNT_STATUS_INACTIVE)
                .setHideAccountFlag(isHideAccount)
                .setAccountNumber("**********"));

        DepositAccount eligibleAccountWillShowFirstAccount = new DepositAccount()
                .setAvailableBalance(null)
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setHideAccountFlag(false)
                .setAccountNumber("**********");

        depositAccounts.addAll(noEligibleAccounts);
        depositAccounts.add(eligibleAccountWillShowFirstAccount);

        when(accountTransferService.getAccountList(correlationId, crmId, false, true))
                .thenReturn(new AccountSaving().setDepositAccountLists(depositAccounts));
    }

    private void mockGetAccountListReturnFirstAccountWillCallGetBalance() throws TMBCommonException {
        DepositAccount firstAccountShouldCallGetBalance = new DepositAccount()
                .setAvailableBalance(null).setAccountNumber("**********")
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE).setTransferOwnTTBMapCode("010");

        DepositAccount accountHaveBalanceShouldNotCallGetBalance = new DepositAccount()
                .setAvailableBalance(new BigDecimal("100.50")).setAccountNumber("**********")
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE).setTransferOwnTTBMapCode("010");

        depositAccountsPassByReference.add(firstAccountShouldCallGetBalance);
        depositAccountsPassByReference.add(accountHaveBalanceShouldNotCallGetBalance);

        when(accountTransferService.getAccountList(correlationId, crmId, false, true))
                .thenReturn(new AccountSaving().setDepositAccountLists(depositAccountsPassByReference));
    }

    private void mockGetAccountListReturnEmptyList() throws TMBCommonException {
        AccountSaving emptyList = new AccountSaving();
        emptyList.setDepositAccountLists(Collections.emptyList());
        when(accountTransferService.getAccountList(correlationId, crmId, false, true))
                .thenReturn(emptyList);
    }

    private void mockGetAccountBalanceReturnAccountBalanceActive() throws TMBCommonException {
        DepositAccount depositAccount = new DepositAccount()
                .setAvailableBalance(new BigDecimal("123.50"))
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setLinkedAccount("**********")
                .setAccountName("accountNameFromGetAvailableBalance");

        accountBalanceResponse.add(depositAccount);
        when(accountTransferService.fetchAccountBalance(any(), any(), any(), any(), any()))
                .thenReturn(depositAccount);
    }

    private void mockGetAccountBalanceReturnOtherStatusAccount() throws TMBCommonException {
        DepositAccount depositAccount = new DepositAccount()
                .setAvailableBalance(new BigDecimal("123.50"))
                .setAccountStatus("OTHER")
                .setLinkedAccount("**********");
        when(accountTransferService.fetchAccountBalance(any(), any(), any(), any(), any()))
                .thenReturn(depositAccount);
    }

    private void mockGetAccountBalanceReturnDormantAccount() throws TMBCommonException {
        DepositAccount depositAccount = new DepositAccount()
                .setAvailableBalance(new BigDecimal("123.50"))
                .setAccountStatus(ACCOUNT_STATUS_DORMANT)
                .setLinkedAccount("**********");
        when(accountTransferService.fetchAccountBalance(any(), any(), any(), any(), any()))
                .thenReturn(depositAccount);
    }

    private void mockGetAccountBalanceReturnCloseAccount() throws TMBCommonException {
        DepositAccount depositAccount = new DepositAccount()
                .setAvailableBalance(new BigDecimal("123.50"))
                .setAccountStatus(ACCOUNT_STATUS_CLOSE)
                .setLinkedAccount("**********");
        when(accountTransferService.fetchAccountBalance(any(), any(), any(), any(), any()))
                .thenReturn(depositAccount);
    }

    private void mockGetAccountBalanceReturnThrowFeignException() throws TMBCommonException {
        when(accountTransferService.fetchAccountBalance(any(), any(), any(), any(), any()))
                .thenThrow(TMBCommonException.class);
    }

    private void assertShouldNotCallGetBalance() throws TMBCommonException {
        verify(accountTransferService, times(0))
                .fetchAccountBalance(any(), any(), any(), any(), any());
    }

    @Test
    void testGetDepositAccountListAndFcdTdMinimumAmountShouldDoesNotThrows() throws TMBCommonException {
        mockGetAccountListReturnFirstAccountNotHaveAvailableBalance(depositAccountsPassByReference);
        mockGetAccountBalanceReturnOtherStatusAccount();
        TmbOneServiceResponse<TransferModuleModel> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(TransferModuleModel.builder()
                .fcdTdMinimumAmount(BigDecimal.valueOf(5000)).build());
        when(transferServiceClient.getAccountConfiguration(anyString()))
                .thenThrow(FeignException.class)
                .thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
        assertDoesNotThrow(() -> service
                .getDepositAccountList(correlationId, crmId, false, true, null));
        assertDoesNotThrow(() -> service
                .getDepositAccountList(correlationId, crmId, false, true, null));
    }

}
