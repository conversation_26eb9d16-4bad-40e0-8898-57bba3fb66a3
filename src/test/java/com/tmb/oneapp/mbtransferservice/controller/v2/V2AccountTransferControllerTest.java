package com.tmb.oneapp.mbtransferservice.controller.v2;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.V2DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferResponse;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.V2AccountBalanceTransferRequest;
import com.tmb.oneapp.mbtransferservice.service.v2.V2AccountTransferService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class V2AccountTransferControllerTest {

    @InjectMocks
    V2AccountTransferController v2accountTransferController;
    @Mock
    V2AccountTransferService v2accountTransferService;

    String crmId;
    String correlationId;
    String accountNumber;
    BigDecimal avAmount;
    String deviceId;
    String falsePreLogin;
    String appVersion;
    String deviceModel;
    String timeStamp;
    V2AccountBalanceTransferRequest request;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        accountNumber = "**********";
        avAmount = new BigDecimal("10000");
        falsePreLogin = "false";
        deviceId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";

        request = new V2AccountBalanceTransferRequest()
                .setAccountType("SDA")
                .setFinancialId("****************");
        request.setAccountNumber(accountNumber);
    }

    @Test
    void testGetV2AccountTransferController() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        V2DepositAccountTransfer accountTransfer = getV2DepositAccountTransfer();
        when(v2accountTransferService.getDepositAccountList(correlationId, crmId, false, true, appVersion))
                .thenReturn(accountTransfer);

        ResponseEntity<TmbOneServiceResponse<V2DepositAccountTransfer>> actual = v2accountTransferController
                .accountTransfer(crmId, correlationId, deviceId, appVersion, deviceModel, timeStamp, new HttpHeaders());

        Assertions.assertNotNull(actual.getBody());
        assertEquals(ResponseCode.SUCCESS.getCode(), actual.getBody().getStatus().getCode());
        assertEquals(1, actual.getBody().getData().getAccountList().size());
        assertEquals(1, actual.getBody().getData().getFcdAccountList().size());
        Assertions.assertFalse(actual.getBody().getData().getAccountList().stream().allMatch(DepositAccountTransfer::getIsFcd));
        Assertions.assertTrue(actual.getBody().getData().getFcdAccountList().stream().allMatch(DepositAccountTransfer::getIsFcd));
    }

    private static V2DepositAccountTransfer getV2DepositAccountTransfer() {
        V2DepositAccountTransfer accountTransfer = new V2DepositAccountTransfer();
        List<DepositAccountTransfer> depositAccountTransferList = new ArrayList<>();
        DepositAccountTransfer depositAccountTransfer = new DepositAccountTransfer();
        depositAccountTransfer.setAccountName("TEST");
        depositAccountTransfer.setIsFcd(false);
        depositAccountTransferList.add(depositAccountTransfer);
        List<DepositAccountTransfer> fcdDepositAccountTransferList = new ArrayList<>();
        DepositAccountTransfer fcdDepositAccountTransfer = new DepositAccountTransfer();
        fcdDepositAccountTransfer.setAccountName("TEST");
        fcdDepositAccountTransfer.setIsFcd(true);
        fcdDepositAccountTransferList.add(fcdDepositAccountTransfer);
        accountTransfer.setAccountList(depositAccountTransferList);
        accountTransfer.setFcdAccountList(fcdDepositAccountTransferList);
        return accountTransfer;
    }

    @Test
    void testGetV2AccountTransferControllerWhenGotExceptionOnGetDepositAccountListShouldThrowsTmbCommonException()
            throws TMBCommonException {
        when(v2accountTransferService.getDepositAccountList(correlationId, crmId, false, true, appVersion))
                .thenThrow(TMBCommonException.class);

        assertThrows(TMBCommonException.class, () -> v2accountTransferController.accountTransfer
                (crmId, correlationId, deviceId, appVersion, deviceModel, timeStamp, new HttpHeaders()));
    }

    @Test
    void testGetAccountBalanceTransfer_Success() throws TMBCommonException {
        when(v2accountTransferService
                .getAccountBalance(anyString(), anyString(), any(), any()))
                .thenReturn(new AccountBalanceTransferResponse());
        request.setIsFcd(true);
        Assertions.assertDoesNotThrow(() -> v2accountTransferController.getAccountBalanceTransfer
                (crmId, correlationId, deviceId, appVersion, deviceModel, timeStamp, new HttpHeaders(), request));
    }

    @Test
    void testGetAccountBalanceTransfer_WhenGotExceptionOnGetAccountBalance_ShouldThrowException()
            throws TMBCommonException {
        when(v2accountTransferService
                .getAccountBalance(anyString(), anyString(), any(), any()))
                .thenThrow(TMBCommonException.class);
        request.setIsFcd(null);
        Assertions.assertThrows(TMBCommonException.class, () -> v2accountTransferController.getAccountBalanceTransfer
                (crmId, correlationId, deviceId, appVersion, deviceModel, timeStamp, new HttpHeaders(), request));
    }

}
