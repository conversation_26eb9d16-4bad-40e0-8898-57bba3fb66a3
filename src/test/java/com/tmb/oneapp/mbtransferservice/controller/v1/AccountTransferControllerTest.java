package com.tmb.oneapp.mbtransferservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.DepositAccountTransfer;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferRequest;
import com.tmb.oneapp.mbtransferservice.model.accountbalance.AccountBalanceTransferResponse;
import com.tmb.oneapp.mbtransferservice.service.AccountTransferServiceImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AccountTransferControllerTest {
    @InjectMocks
    AccountTransferController accountTransferController;
    @Mock
    AccountTransferServiceImpl accountTransferService;

    String crmId;
    String correlationId;
    String accountNumber;
    BigDecimal avAmount;
    String deviceId;
    String falsePreLogin;
    String appVersion;
    String deviceModel;
    String timeStamp;
    AccountBalanceTransferRequest request;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        accountNumber = "**********";
        avAmount = new BigDecimal("10000");
        falsePreLogin = "false";
        deviceId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";

        request = new AccountBalanceTransferRequest().setAccountNumber(accountNumber);
    }

    @Test
    void testGetAccountTransferController() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        List<DepositAccountTransfer> depositAccountTransferList = new ArrayList<>();
        DepositAccountTransfer depositAccountTransfer = new DepositAccountTransfer();
        depositAccountTransfer.setAccountName("TEST");
        depositAccountTransferList.add(depositAccountTransfer);
        when(accountTransferService.getDepositAccountList(correlationId, crmId, false, true)).thenReturn(depositAccountTransferList);

        ResponseEntity<TmbOneServiceResponse<List<DepositAccountTransfer>>> actual = accountTransferController.accountTransfer(crmId, correlationId, deviceId, appVersion, deviceModel, timeStamp, new HttpHeaders());

        assertEquals(ResponseCode.SUCCESS.getCode(), actual.getBody().getStatus().getCode());
        assertEquals(1, actual.getBody().getData().size());
    }

    @Test
    void testGetAccountTransferControllerWhenGotExceptionOnGetDepositAccountListShouldThrowsTmbCommonException() throws TMBCommonException {
        when(accountTransferService.getDepositAccountList(correlationId, crmId, false, true)).thenThrow(TMBCommonException.class);

        assertThrows(TMBCommonException.class, () -> accountTransferController.accountTransfer(crmId, correlationId, deviceId, appVersion, deviceModel, timeStamp, new HttpHeaders()));
    }

    @Test
    void testGetAccountBalanceTransfer_Success() throws TMBCommonException {
        when(accountTransferService.getAccountBalance(eq(accountNumber), any(HttpHeaders.class))).thenReturn(new AccountBalanceTransferResponse());

        Assertions.assertDoesNotThrow(() -> accountTransferController.getAccountBalanceTransfer(crmId, correlationId, deviceId, appVersion, deviceModel, timeStamp, new HttpHeaders(), request));
    }

    @Test
    void testGetAccountBalanceTransfer_WhenGotExceptionOnGetAccountBalance_ShouldThrowException() throws TMBCommonException {
        when(accountTransferService.getAccountBalance(eq(accountNumber), any(HttpHeaders.class))).thenThrow(TMBCommonException.class);

        Assertions.assertThrows(TMBCommonException.class, () -> accountTransferController.getAccountBalanceTransfer(crmId, correlationId, deviceId, appVersion, deviceModel, timeStamp, new HttpHeaders(), request));
    }
}
