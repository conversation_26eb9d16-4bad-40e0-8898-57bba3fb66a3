package com.tmb.oneapp.mbtransferservice.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbStatus;
import com.tmb.oneapp.mbtransferservice.constant.ResponseCode;
import com.tmb.oneapp.mbtransferservice.model.BaseTransferRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.offus.TransferOffUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.MbTransferOnUsValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.onus.TransferOnUsValidateResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayConfirmResponse;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateRequest;
import com.tmb.oneapp.mbtransferservice.model.promptpay.MbTransferPromptpayValidateResponse;
import com.tmb.oneapp.mbtransferservice.service.TransferServiceImpl;
import feign.FeignException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TransferControllerTest {

    @Mock
    TransferServiceImpl transferService;

    @InjectMocks
    TransferController transferController;

    String crmId;
    String correlationId;
    String accountNumber;
    BigDecimal avAmount;
    String deviceId;
    String falsePreLogin;
    String appVersion;
    String deviceModel;
    String timeStamp;
    ValidatorFactory validatorFactory;
    Validator validator;

    @BeforeEach
    void setUp() {
        crmId = "001100000000000000000001184383";
        correlationId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        accountNumber = "**********";
        avAmount = new BigDecimal("10000");
        falsePreLogin = "false";
        deviceId = "32fbd3b2-3f97-4a89-ar39-b4f628fbc8da";
        validatorFactory = Validation.buildDefaultValidatorFactory();
        validator = validatorFactory.getValidator();
    }

    private MbTransferPromptpayValidateResponse mockPromptpayValidate(){
        MbTransferPromptpayValidateResponse response = new MbTransferPromptpayValidateResponse();
        response.setIsRequireConfirmPin(true);
        response.setTransId("Test");
        response.setFee("10");
        response.setAmount("100");
        response.setToAccountName("Test");
        return  response;
    }

    @AfterEach
    void close() {
        validatorFactory.close();
    }

    @ParameterizedTest
    @CsvSource({
            ", toAccountNo, 10.00",
            "fromAccountNo, , 10.00",
            "fromAccountNo, toAccountNo, ",
            "fromAccountNo, , ",
            ", toAccountNo, ",
            ", , 10.00",
            ", , "
    })
    void testValidateBaseTransferRequestModel_WhenMandatoryFieldBlank_ShouldFailed(String fromAccountNo, String toAccountNo, String amount) {
        BaseTransferRequest baseTransferRequest = new BaseTransferRequest();
        baseTransferRequest.setFromAccountNo(fromAccountNo);
        baseTransferRequest.setToAccountNo(toAccountNo);
        baseTransferRequest.setAmount(amount);

        Set<ConstraintViolation<BaseTransferRequest>> violations = validator.validate(baseTransferRequest);

        System.out.println(violations);
        Assertions.assertFalse(violations.isEmpty());
    }

    @Test
    void testPromptpayValidateController() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        when(transferService.promptpayValidate(anyString(),anyString(),anyString(),anyString(),any(), any(MbTransferPromptpayValidateRequest.class))).thenReturn(mockPromptpayValidate());
        ResponseEntity<TmbOneServiceResponse<MbTransferPromptpayValidateResponse>> result = transferController.promptpayValidate(crmId,deviceId,falsePreLogin,correlationId,new HttpHeaders(),new MbTransferPromptpayValidateRequest());
        Assertions.assertEquals("0000",result.getBody().getStatus().getCode());
        Assertions.assertEquals("success",result.getBody().getStatus().getMessage());
        Assertions.assertEquals("Test",result.getBody().getData().getTransId());
    }

    @Test
    void testPromptpayValidateControllerNull() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        when(transferService.promptpayValidate(anyString(),anyString(),anyString(),anyString(),any(),any(MbTransferPromptpayValidateRequest.class))).thenReturn(null);
        ResponseEntity<TmbOneServiceResponse<MbTransferPromptpayValidateResponse>> result = transferController.promptpayValidate(crmId,deviceId,falsePreLogin,correlationId,new HttpHeaders(),new MbTransferPromptpayValidateRequest());
        Assertions.assertEquals(HttpStatus.BAD_REQUEST,result.getStatusCode());
        Assertions.assertEquals("0010",result.getBody().getStatus().getCode());
        Assertions.assertEquals("Invalid Request",result.getBody().getStatus().getMessage());
    }

    @Test
    void testPromptpayValidateControllerTmbException() throws TMBCommonException {

        when(transferService.promptpayValidate(anyString(),anyString(),anyString(),anyString(),any(),any(MbTransferPromptpayValidateRequest.class))).thenThrow(new TMBCommonException(
                ResponseCode.FAILED.getCode(),
                ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(),
                HttpStatus.OK,
                null));

        Assertions.assertThrows(TMBCommonException.class,()->{
            transferController.promptpayValidate(crmId,deviceId,falsePreLogin,correlationId,new HttpHeaders(),new MbTransferPromptpayValidateRequest());
        });
    }

    @Test
    void testOffUsValidateWhenValidThenReturnSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        when(transferService.offUsValidate(anyString(),anyString(),any(),any())).thenReturn(mockTmbOffUsValidateResponse());
        ResponseEntity<TmbOneServiceResponse<TransferOffUsValidateResponse>> result = transferController.offUsValidate(crmId,correlationId,new HttpHeaders(),mockTmbOffUsValidateRequest());
        Assertions.assertEquals("0000",result.getBody().getStatus().getCode());
        Assertions.assertEquals("success",result.getBody().getStatus().getMessage());
        Assertions.assertEquals("01",result.getBody().getData().getTransId());
        verify(transferService,times(1)).offUsValidate(anyString(),anyString(),any(),any());
    }

    @Test
    void testOffUsConfirmWhenValidThenReturnSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        when(transferService.offUsConfirm(anyString(),anyString(),anyString(),anyString(),anyString(),anyString(),any(),any())).thenReturn(mockTmbOffUsConfirmResponse());
        ResponseEntity<TmbOneServiceResponse<TransferOffUsConfirmResponse>> result = transferController.offUsConfirm(crmId,correlationId,"","","","",new HttpHeaders(),mockTmbOffUsConfirmRequest());
        Assertions.assertEquals("0000",result.getBody().getStatus().getCode());
        Assertions.assertEquals("success",result.getBody().getStatus().getMessage());
        verify(transferService,times(1)).offUsConfirm(anyString(),anyString(),anyString(),anyString(),anyString(),anyString(),any(),any());
    }

    @Test
    void testOffUsConfirmWhenSvcThrowErrorThenThrowTMBCommonException() throws TMBCommonException {
        when(transferService.offUsConfirm(anyString(),anyString(),anyString(),anyString(),anyString(),anyString(),any(),any())).thenThrow(FeignException.FeignClientException.class);
        Assertions.assertThrows(TMBCommonException.class, () -> transferController.offUsConfirm(correlationId, crmId, "","","","",new HttpHeaders(), mockTmbOffUsConfirmRequest()));
        verify(transferService,times(1)).offUsConfirm(anyString(),anyString(),anyString(),anyString(),anyString(),anyString(),any(),any());
    }

    private TransferOffUsConfirmRequest mockTmbOffUsConfirmRequest(){
        TransferOffUsConfirmRequest req = new TransferOffUsConfirmRequest();
        req.setTransId("01");
        return req;
    }

    private TmbOneServiceResponse<TransferOffUsConfirmResponse> mockTmbOffUsConfirmResponse(){
        TransferOffUsConfirmResponse res = new TransferOffUsConfirmResponse();
        res.setReferenceNo("0001");
        res.setIsToOwnAccount(false);
        res.setRemainingBalance("10000");
        res.setQr("");
        TmbOneServiceResponse<TransferOffUsConfirmResponse> tmbRes = new TmbOneServiceResponse<>();
        tmbRes.setData(res);
        tmbRes.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        return tmbRes;
    }

    private TransferOffUsValidateRequest mockTmbOffUsValidateRequest(){
        TransferOffUsValidateRequest req = new TransferOffUsValidateRequest();
        req.setFlow("Setting");
        req.setToFavoriteName("favName");
        req.setToBankCode("01");
        req.setAmount("10000");
        req.setDepositNo("depositNo");
        req.setFromAccountNo(accountNumber);
        return req;
    }

    private TmbOneServiceResponse<TransferOffUsValidateResponse> mockTmbOffUsValidateResponse(){
        TransferOffUsValidateResponse res = new TransferOffUsValidateResponse();
        res.setTransId("01");
        res.setAmount("10000");
        res.setFee("0");
        res.setToAccountName("000001");
        res.setIsRequireConfirmPin(false);

        TmbOneServiceResponse<TransferOffUsValidateResponse> tmbRes = new TmbOneServiceResponse<>();
        tmbRes.setData(res);
        tmbRes.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        return tmbRes;
    }


    private TmbOneServiceResponse<TransferOnUsValidateResponse> mockTmbOneServiceResponseForOnUsValidate() {
        TransferOnUsValidateResponse transferVerifyResponse = new TransferOnUsValidateResponse();
        transferVerifyResponse.setTransId("transId");

        TmbOneServiceResponse<TransferOnUsValidateResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(transferVerifyResponse);
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        return tmbOneServiceResponse;
    }

    private TmbOneServiceResponse<TransferOnUsConfirmResponse> mockTmbOneServiceResponseForOnUsConfirm(){
        TransferOnUsConfirmResponse transferOnUsConfirmResponse = new TransferOnUsConfirmResponse();
        transferOnUsConfirmResponse.setReferenceNo("1234");

        TmbOneServiceResponse<TransferOnUsConfirmResponse> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(transferOnUsConfirmResponse);
        tmbOneServiceResponse.setStatus(new TmbStatus(ResponseCode.SUCCESS.getCode(),ResponseCode.SUCCESS.getMessage(), ResponseCode.SUCCESS.getService()));
        return tmbOneServiceResponse;
    }

    @Test
    void onUsValidateTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        when(transferService.onUsValidate(any(),any(),any(),any())).thenReturn(mockTmbOneServiceResponseForOnUsValidate());

        ResponseEntity<TmbOneServiceResponse<TransferOnUsValidateResponse>> transferOnUsValidateResponse = transferController.onUsValidate("", "",new HttpHeaders(), new MbTransferOnUsValidateRequest());

        verify(transferService, times(1)).onUsValidate(any(),any(),any(),any());
        Assertions.assertEquals(mockTmbOneServiceResponseForOnUsValidate().getData().getTransId(),transferOnUsValidateResponse.getBody().getData().getTransId());
    }

    @Test
    void onUsValidateExceptionCase() throws TMBCommonException {
        when(transferService.onUsValidate(any(),any(),any(),any())).thenThrow(new TMBCommonException(
                ResponseCode.FAILED.getCode(),
                ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(),
                HttpStatus.OK,
                null));

        Assertions.assertThrows(TMBCommonException.class, () -> {
            transferController.onUsValidate("", "",new HttpHeaders(), new MbTransferOnUsValidateRequest());
        });
    }

    @Test
    void onUsComfirmTest() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        when(transferService.onUsConfirm(any(),any(),any(),any())).thenReturn(mockTmbOneServiceResponseForOnUsConfirm());

        ResponseEntity<TmbOneServiceResponse<TransferOnUsConfirmResponse>> transferOnUsConfirmResponse = transferController.onUsConfirm("", "","","","" ,"",new HttpHeaders(),new MbTransferOnUsConfirmRequest());

        verify(transferService, times(1)).onUsConfirm(any(),any(),any(),any());
        Assertions.assertEquals(mockTmbOneServiceResponseForOnUsConfirm().getData().getReferenceNo(),transferOnUsConfirmResponse.getBody().getData().getReferenceNo());
    }

    @Test
    void onUsComfirmExceptionCase() throws TMBCommonException {
        when(transferService.onUsConfirm(any(),any(),any(),any())).thenThrow(new TMBCommonException(
                ResponseCode.FAILED.getCode(),
                ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(),
                HttpStatus.OK,
                null));

        Assertions.assertThrows(TMBCommonException.class, () -> {
            transferController.onUsConfirm("", "","","","" ,"",new HttpHeaders(),new MbTransferOnUsConfirmRequest());
        });
    }



    private MbTransferPromptpayConfirmResponse mockPromptpayConfirm(){
        MbTransferPromptpayConfirmResponse response = new MbTransferPromptpayConfirmResponse();
        response.setQr("Qr");
        response.setTransferCreatedDatetime("yyyy-MM-dd'T'HH:mm:ss");
        response.setRemainingBalance("1000");
        response.setIsToOwnAccount(true);
        response.setReferenceNo("Test");
        return  response;
    }
    @Test
    void testPromptpayConfirmController() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        //TransferServiceImpl transferService = mock(TransferServiceImpl.class);
        when(transferService.promptpayConfirm(any(),any(),any(),any(),any(),any(),any(),any(MbTransferPromptpayConfirmRequest.class))).thenReturn(mockPromptpayConfirm());
        ResponseEntity<TmbOneServiceResponse<MbTransferPromptpayConfirmResponse>> result = transferController.promptpayConfirm(crmId,correlationId,deviceId,appVersion,deviceModel,timeStamp,new HttpHeaders(),new MbTransferPromptpayConfirmRequest());
        Assertions.assertEquals("0000",result.getBody().getStatus().getCode());
        Assertions.assertEquals("success",result.getBody().getStatus().getMessage());
        Assertions.assertEquals("Test",result.getBody().getData().getReferenceNo());
    }

    @Test
    void testPromptpayConfirmControllerNull() throws TMBCommonException, TMBCustomCommonExceptionWithResponse {
        when(transferService.promptpayConfirm(any(),any(),any(),any(),any(),any(),any(),any(MbTransferPromptpayConfirmRequest.class))).thenReturn(null);
        ResponseEntity<TmbOneServiceResponse<MbTransferPromptpayConfirmResponse>> result = transferController.promptpayConfirm(crmId,correlationId,deviceId,appVersion,deviceModel,timeStamp,new HttpHeaders(),new MbTransferPromptpayConfirmRequest());
        Assertions.assertEquals(HttpStatus.BAD_REQUEST,result.getStatusCode());
        Assertions.assertEquals("0010",result.getBody().getStatus().getCode());
        Assertions.assertEquals("Invalid Request",result.getBody().getStatus().getMessage());
    }

    @Test
    void testPromptpayConfirmControllerTmbException() throws TMBCommonException {

        when(transferService.promptpayConfirm(anyString(),anyString(),anyString(),anyString(),anyString(),anyString(),any(),any(MbTransferPromptpayConfirmRequest.class))).thenThrow(new TMBCommonException(
                ResponseCode.FAILED.getCode(),
                ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(),
                HttpStatus.OK,
                null));

        Assertions.assertThrows(TMBCommonException.class,()->{
            transferController.promptpayConfirm(crmId,correlationId,deviceId,appVersion,deviceModel,timeStamp,new HttpHeaders(),new MbTransferPromptpayConfirmRequest());
        });


    }

}
